@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-b62b882e, scroll-view.data-v-b62b882e, swiper-item.data-v-b62b882e {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-reset-button.data-v-b62b882e {
  width: 100%;
}
.u-action-sheet.data-v-b62b882e {
  text-align: center;
}
.u-action-sheet__header.data-v-b62b882e {
  position: relative;
  padding: 12px 30px;
}
.u-action-sheet__header__title.data-v-b62b882e {
  font-size: 16px;
  color: #303133;
  font-weight: bold;
  text-align: center;
}
.u-action-sheet__header__icon-wrap.data-v-b62b882e {
  position: absolute;
  right: 15px;
  top: 15px;
}
.u-action-sheet__description.data-v-b62b882e {
  font-size: 13px;
  color: #909193;
  margin: 18px 15px;
  text-align: center;
}
.u-action-sheet__item-wrap__item.data-v-b62b882e {
  padding: 15px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.u-action-sheet__item-wrap__item__name.data-v-b62b882e {
  font-size: 16px;
  color: #303133;
  text-align: center;
}
.u-action-sheet__item-wrap__item__subname.data-v-b62b882e {
  font-size: 13px;
  color: #c0c4cc;
  margin-top: 10px;
  text-align: center;
}
.u-action-sheet__cancel-text.data-v-b62b882e {
  font-size: 16px;
  color: #606266;
  text-align: center;
  padding: 16px;
}
.u-action-sheet--hover.data-v-b62b882e {
  background-color: #f2f3f5;
}

