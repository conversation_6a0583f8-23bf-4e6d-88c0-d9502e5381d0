{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/code/work/rescue/pages/index/index.vue?5ff2", "webpack:///D:/code/work/rescue/pages/index/index.vue?9fe7", "webpack:///D:/code/work/rescue/pages/index/index.vue?55fc", "webpack:///D:/code/work/rescue/pages/index/index.vue?26c0", "uni-app:///pages/index/index.vue", "webpack:///D:/code/work/rescue/pages/index/index.vue?dc92", "webpack:///D:/code/work/rescue/pages/index/index.vue?ab14"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "form", "carType", "phone", "licensePlate", "rescueItem", "hasSign", "address", "message", "rules", "required", "trigger", "pattern", "carTypeColumns", "showCarTypePicker", "showNumberKeyboard", "showLicensePlatePicker", "submitting", "onLoad", "methods", "confirmCarType", "chooseLocation", "uni", "success", "fail", "console", "title", "content", "showCancel", "getCurrentLocation", "type", "submitForm", "response", "icon", "setTimeout", "submitRescueRequest", "requestData", "submitTime", "resetForm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACqM;AACrM,gBAAgB,4MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,2UAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,mWAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxFA;AAAA;AAAA;AAAA;AAA4wB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC+HhyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;QACAP,UACA;UACAQ;UACAF;UACAG;QACA,EACA;QACAR,QACA;UACAO;UACAF;UACAG;QACA,GACA;UACAC;UACAJ;UACAG;QACA,EACA;QACAP,eACA;UACAM;UACAF;UACAG;QACA,EACA;QACAL,UACA;UACAI;UACAF;UACAG;QACA,EACA;QACAJ,UACA;UACAG;UACAF;UACAG;QACA;MAEA;MACA;MACAE,iBACA,qDACA;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACAC;QACAC;UACA;QACA;QACAC;UACAC;UACA;UACAH;YACAI;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACAP;QACAQ;QACAP;UACA;UACAE;QACA;QACAD;UACAC;QACA;MACA;IACA;IAEA;IACAM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAN;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAO;gBAEAV;kBACAI;kBACAO;gBACA;;gBAEA;gBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAT;gBACAH;kBACAI;kBACAO;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAlC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACA6B;gBACA,GAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;QACApC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnTA;AAAA;AAAA;AAAA;AAA2nC,CAAgB,qlCAAG,EAAC,C;;;;;;;;;;;ACA/oC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form/u-form\" */ \"@/uni_modules/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-radio-group/u-radio-group\" */ \"@/uni_modules/uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-radio/u-radio\" */ \"@/uni_modules/uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-picker/u-picker\" */ \"@/uni_modules/uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uNumberKeyboard: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-number-keyboard/u-number-keyboard\" */ \"@/uni_modules/uview-ui/components/u-number-keyboard/u-number-keyboard.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showCarTypePicker = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showNumberKeyboard = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.showLicensePlatePicker = true\n    }\n    _vm.e3 = function ($event) {\n      _vm.showCarTypePicker = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.showNumberKeyboard = false\n    }\n    _vm.e5 = function ($event) {\n      _vm.showLicensePlatePicker = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<!-- 顶部背景图 -->\r\n\t\t<view class=\"header-bg\">\r\n\t\t\t<image class=\"bg-image\" src=\"/static/rescue-bg.jpg\" mode=\"aspectFill\"></image>\r\n\t\t\t<view class=\"header-content\">\r\n\t\t\t\t<text class=\"header-title\">道路救援服务</text>\r\n\t\t\t\t<text class=\"header-subtitle\">24小时为您提供专业救援</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 表单内容 -->\r\n\t\t<view class=\"form-container\">\r\n\t\t\t<u-form :model=\"form\" ref=\"uForm\" :rules=\"rules\" :errorType=\"['toast']\">\r\n\t\t\t\t<!-- 车型选择 -->\r\n\t\t\t\t<u-form-item label=\"车型\" prop=\"carType\" required>\r\n\t\t\t\t\t<u-input\r\n\t\t\t\t\t\tv-model=\"form.carType\"\r\n\t\t\t\t\t\tplaceholder=\"请选择车型\"\r\n\t\t\t\t\t\t:readonly=\"true\"\r\n\t\t\t\t\t\t@click=\"showCarTypePicker = true\"\r\n\t\t\t\t\t\tsuffix-icon=\"arrow-down\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</u-form-item>\r\n\r\n\t\t\t\t<!-- 手机号 -->\r\n\t\t\t\t<u-form-item label=\"手机号\" prop=\"phone\" required>\r\n\t\t\t\t\t<u-input\r\n\t\t\t\t\t\tv-model=\"form.phone\"\r\n\t\t\t\t\t\tplaceholder=\"请输入手机号\"\r\n\t\t\t\t\t\t@click=\"showNumberKeyboard = true\"\r\n\t\t\t\t\t\t:readonly=\"true\"\r\n\t\t\t\t\t\tmaxlength=\"11\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</u-form-item>\r\n\r\n\t\t\t\t<!-- 车牌号 -->\r\n\t\t\t\t<u-form-item label=\"车牌号\" prop=\"licensePlate\" required>\r\n\t\t\t\t\t<u-input\r\n\t\t\t\t\t\tv-model=\"form.licensePlate\"\r\n\t\t\t\t\t\tplaceholder=\"请输入车牌号\"\r\n\t\t\t\t\t\t@click=\"showLicensePlatePicker = true\"\r\n\t\t\t\t\t\t:readonly=\"true\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</u-form-item>\r\n\r\n\t\t\t\t<!-- 救援事项 -->\r\n\t\t\t\t<u-form-item label=\"救援事项\" prop=\"rescueItem\">\r\n\t\t\t\t\t<u-input\r\n\t\t\t\t\t\tv-model=\"form.rescueItem\"\r\n\t\t\t\t\t\tplaceholder=\"请描述需要救援的问题\"\r\n\t\t\t\t\t\ttype=\"textarea\"\r\n\t\t\t\t\t\t:autoHeight=\"true\"\r\n\t\t\t\t\t\tmaxlength=\"200\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</u-form-item>\r\n\r\n\t\t\t\t<!-- 是否有标 -->\r\n\t\t\t\t<u-form-item label=\"是否有标\" prop=\"hasSign\" required>\r\n\t\t\t\t\t<u-radio-group v-model=\"form.hasSign\" placement=\"row\">\r\n\t\t\t\t\t\t<u-radio label=\"是\" name=\"1\"></u-radio>\r\n\t\t\t\t\t\t<u-radio label=\"否\" name=\"0\"></u-radio>\r\n\t\t\t\t\t</u-radio-group>\r\n\t\t\t\t</u-form-item>\r\n\r\n\t\t\t\t<!-- 地址 -->\r\n\t\t\t\t<u-form-item label=\"地址\" prop=\"address\" required>\r\n\t\t\t\t\t<u-input\r\n\t\t\t\t\t\tv-model=\"form.address\"\r\n\t\t\t\t\t\tplaceholder=\"点击获取当前位置或手动输入\"\r\n\t\t\t\t\t\t@click=\"chooseLocation\"\r\n\t\t\t\t\t\tsuffix-icon=\"map\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</u-form-item>\r\n\r\n\t\t\t\t<!-- 留言 -->\r\n\t\t\t\t<u-form-item label=\"留言\" prop=\"message\">\r\n\t\t\t\t\t<u-input\r\n\t\t\t\t\t\tv-model=\"form.message\"\r\n\t\t\t\t\t\tplaceholder=\"其他需要说明的情况\"\r\n\t\t\t\t\t\ttype=\"textarea\"\r\n\t\t\t\t\t\t:autoHeight=\"true\"\r\n\t\t\t\t\t\tmaxlength=\"300\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t</u-form>\r\n\r\n\t\t\t<!-- 提交按钮 -->\r\n\t\t\t<view class=\"submit-btn-container\">\r\n\t\t\t\t<u-button\r\n\t\t\t\t\ttype=\"primary\"\r\n\t\t\t\t\tsize=\"large\"\r\n\t\t\t\t\t:loading=\"submitting\"\r\n\t\t\t\t\t@click=\"submitForm\"\r\n\t\t\t\t\tcustomStyle=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{{ submitting ? '提交中...' : '立即申请救援' }}\r\n\t\t\t\t</u-button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 车型选择器 -->\r\n\t\t<u-picker\r\n\t\t\t:show=\"showCarTypePicker\"\r\n\t\t\t:columns=\"carTypeColumns\"\r\n\t\t\t@confirm=\"confirmCarType\"\r\n\t\t\t@cancel=\"showCarTypePicker = false\"\r\n\t\t></u-picker>\r\n\r\n\t\t<!-- 数字键盘 -->\r\n\t\t<u-number-keyboard\r\n\t\t\tv-model=\"form.phone\"\r\n\t\t\t:show=\"showNumberKeyboard\"\r\n\t\t\tmode=\"car\"\r\n\t\t\t@close=\"showNumberKeyboard = false\"\r\n\t\t></u-number-keyboard>\r\n\r\n\t\t<!-- 车牌键盘 -->\r\n\t\t<u-license-plate-keyboard\r\n\t\t\tv-model=\"form.licensePlate\"\r\n\t\t\t:show=\"showLicensePlatePicker\"\r\n\t\t\t@close=\"showLicensePlatePicker = false\"\r\n\t\t></u-license-plate-keyboard>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { post } from '@/utils/request.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 表单数据\r\n\t\t\tform: {\r\n\t\t\t\tcarType: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tlicensePlate: '',\r\n\t\t\t\trescueItem: '',\r\n\t\t\t\thasSign: '',\r\n\t\t\t\taddress: '',\r\n\t\t\t\tmessage: ''\r\n\t\t\t},\r\n\t\t\t// 表单验证规则\r\n\t\t\trules: {\r\n\t\t\t\tcarType: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请选择车型',\r\n\t\t\t\t\t\ttrigger: 'change'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tphone: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请输入手机号',\r\n\t\t\t\t\t\ttrigger: 'change'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tpattern: /^1[3-9]\\d{9}$/,\r\n\t\t\t\t\t\tmessage: '请输入正确的手机号',\r\n\t\t\t\t\t\ttrigger: 'change'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tlicensePlate: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请输入车牌号',\r\n\t\t\t\t\t\ttrigger: 'change'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\thasSign: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请选择是否有标',\r\n\t\t\t\t\t\ttrigger: 'change'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\taddress: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请选择或输入地址',\r\n\t\t\t\t\t\ttrigger: 'change'\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t},\r\n\t\t\t// 车型选项\r\n\t\t\tcarTypeColumns: [\r\n\t\t\t\t['轿车', 'SUV', '面包车', '货车', '客车', '摩托车', '电动车', '其他']\r\n\t\t\t],\r\n\t\t\t// 控制显示状态\r\n\t\t\tshowCarTypePicker: false,\r\n\t\t\tshowNumberKeyboard: false,\r\n\t\t\tshowLicensePlatePicker: false,\r\n\t\t\tsubmitting: false\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// 页面加载时可以获取当前位置\r\n\t\tthis.getCurrentLocation()\r\n\t},\r\n\tmethods: {\r\n\t\t// 车型选择确认\r\n\t\tconfirmCarType(value) {\r\n\t\t\tthis.form.carType = value.value[0]\r\n\t\t\tthis.showCarTypePicker = false\r\n\t\t},\r\n\r\n\t\t// 选择位置\r\n\t\tchooseLocation() {\r\n\t\t\tuni.chooseLocation({\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tthis.form.address = res.address + ' ' + res.name\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.log('选择位置失败:', err)\r\n\t\t\t\t\t// 如果用户拒绝授权，可以手动输入\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '获取位置失败，您可以手动输入地址',\r\n\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 获取当前位置\r\n\t\tgetCurrentLocation() {\r\n\t\t\tuni.getLocation({\r\n\t\t\t\ttype: 'gcj02',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t// 可以根据经纬度反向解析地址\r\n\t\t\t\t\tconsole.log('当前位置:', res)\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.log('获取位置失败:', err)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 表单提交\r\n\t\tasync submitForm() {\r\n\t\t\t// 表单验证\r\n\t\t\ttry {\r\n\t\t\t\tawait this.$refs.uForm.validate()\r\n\t\t\t} catch (errors) {\r\n\t\t\t\tconsole.log('表单验证失败:', errors)\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\r\n\t\t\tthis.submitting = true\r\n\r\n\t\t\ttry {\r\n\t\t\t\t// 调用后端接口\r\n\t\t\t\tconst response = await this.submitRescueRequest()\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '提交成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t})\r\n\r\n\t\t\t\t// 提交成功后可以跳转到结果页面或重置表单\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.resetForm()\r\n\t\t\t\t}, 1500)\r\n\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('提交失败:', error)\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '提交失败，请重试',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t} finally {\r\n\t\t\t\tthis.submitting = false\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 调用后端接口提交救援请求\r\n\t\tasync submitRescueRequest() {\r\n\t\t\tconst requestData = {\r\n\t\t\t\tcarType: this.form.carType,\r\n\t\t\t\tphone: this.form.phone,\r\n\t\t\t\tlicensePlate: this.form.licensePlate,\r\n\t\t\t\trescueItem: this.form.rescueItem,\r\n\t\t\t\thasSign: this.form.hasSign,\r\n\t\t\t\taddress: this.form.address,\r\n\t\t\t\tmessage: this.form.message,\r\n\t\t\t\tsubmitTime: new Date().toISOString()\r\n\t\t\t}\r\n\r\n\t\t\t// 调用封装的post方法\r\n\t\t\treturn await post('/rescue/submit', requestData)\r\n\t\t},\r\n\r\n\t\t// 重置表单\r\n\t\tresetForm() {\r\n\t\t\tthis.form = {\r\n\t\t\t\tcarType: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tlicensePlate: '',\r\n\t\t\t\trescueItem: '',\r\n\t\t\t\thasSign: '',\r\n\t\t\t\taddress: '',\r\n\t\t\t\tmessage: ''\r\n\t\t\t}\r\n\t\t\tthis.$refs.uForm.clearValidate()\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n<style scoped>\r\n.page {\r\n\tbackground-color: #f5f5f5;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n/* 顶部背景区域 */\r\n.header-bg {\r\n\tposition: relative;\r\n\theight: 400rpx;\r\n\toverflow: hidden;\r\n}\r\n\r\n.bg-image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n}\r\n\r\n.header-content {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.4);\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tcolor: white;\r\n}\r\n\r\n.header-title {\r\n\tfont-size: 48rpx;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 20rpx;\r\n\ttext-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.header-subtitle {\r\n\tfont-size: 28rpx;\r\n\topacity: 0.9;\r\n\ttext-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n/* 表单容器 */\r\n.form-container {\r\n\tmargin: -60rpx 30rpx 30rpx 30rpx;\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 40rpx 30rpx;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n\tposition: relative;\r\n\tz-index: 2;\r\n}\r\n\r\n/* 表单项样式调整 */\r\n.form-container :deep(.u-form-item) {\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.form-container :deep(.u-form-item__label) {\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tfont-size: 32rpx;\r\n}\r\n\r\n.form-container :deep(.u-input__content) {\r\n\tbackground-color: #f8f9fa;\r\n\tborder-radius: 12rpx;\r\n\tborder: 2rpx solid #e9ecef;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.form-container :deep(.u-input__content:focus-within) {\r\n\tborder-color: #667eea;\r\n\tbackground-color: white;\r\n}\r\n\r\n.form-container :deep(.u-textarea) {\r\n\tbackground-color: #f8f9fa;\r\n\tborder-radius: 12rpx;\r\n\tborder: 2rpx solid #e9ecef;\r\n\tmin-height: 120rpx;\r\n}\r\n\r\n/* 单选按钮样式 */\r\n.form-container :deep(.u-radio-group) {\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.form-container :deep(.u-radio) {\r\n\tmargin-right: 40rpx;\r\n}\r\n\r\n.form-container :deep(.u-radio__icon-wrap) {\r\n\tborder-color: #667eea;\r\n}\r\n\r\n.form-container :deep(.u-radio__icon-wrap--checked) {\r\n\tbackground-color: #667eea;\r\n}\r\n\r\n/* 提交按钮容器 */\r\n.submit-btn-container {\r\n\tmargin-top: 60rpx;\r\n\tpadding: 0 20rpx;\r\n}\r\n\r\n.submit-btn-container :deep(.u-button) {\r\n\theight: 88rpx;\r\n\tborder-radius: 44rpx;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n\tbox-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.submit-btn-container :deep(.u-button:active) {\r\n\ttransform: translateY(2rpx);\r\n\tbox-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n/* 响应式调整 */\r\n@media screen and (max-width: 750rpx) {\r\n\t.form-container {\r\n\t\tmargin: -40rpx 20rpx 20rpx 20rpx;\r\n\t\tpadding: 30rpx 20rpx;\r\n\t}\r\n\r\n\t.header-title {\r\n\t\tfont-size: 42rpx;\r\n\t}\r\n\r\n\t.header-subtitle {\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n}\r\n\r\n/* 动画效果 */\r\n.form-container {\r\n\tanimation: slideUp 0.6s ease-out;\r\n}\r\n\r\n@keyframes slideUp {\r\n\tfrom {\r\n\t\ttransform: translateY(100rpx);\r\n\t\topacity: 0;\r\n\t}\r\n\tto {\r\n\t\ttransform: translateY(0);\r\n\t\topacity: 1;\r\n\t}\r\n}\r\n\r\n/* 输入框聚焦效果 */\r\n.form-container :deep(.u-input__content) {\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.form-container :deep(.u-input__content::before) {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: -100%;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tbackground: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);\r\n\ttransition: left 0.5s;\r\n}\r\n\r\n.form-container :deep(.u-input__content:focus-within::before) {\r\n\tleft: 100%;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755347595336\n      var cssReload = require(\"D:/soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}