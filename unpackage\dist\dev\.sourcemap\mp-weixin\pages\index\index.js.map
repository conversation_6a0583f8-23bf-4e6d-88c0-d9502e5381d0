{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/code/work/rescue/pages/index/index.vue?9fe7", "webpack:///D:/code/work/rescue/pages/index/index.vue?55fc", "webpack:///D:/code/work/rescue/pages/index/index.vue?26c0", "uni-app:///pages/index/index.vue", "webpack:///D:/code/work/rescue/pages/index/index.vue?4fa7", "webpack:///D:/code/work/rescue/pages/index/index.vue?550a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uEmpty", "data", "list", "tabsList", "serverSiteList", "loginStatus", "zxList", "computed", "onLoad", "onShow", "onReady", "methods", "getUserInfo", "console", "getPhoneNumber", "uni", "provider", "success", "encryptedData", "iv", "code", "that", "getZXMap", "name", "id", "getZXList", "zxType", "getCurrentLocation", "type", "lat", "lng", "fail", "title", "getCityName", "handlerClick", "url", "handlerCutNews", "handlerDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqM;AACrM,gBAAgB,4MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAA4wB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACkDhyB;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC,OACA,oFACA,oFACA,mFACA;MACAC;MACAC;MAEAC;MACAC;IACA;EACA;EACAC,4BACA,2CACA;EACAC;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;EACA;EACAC,6BACA;EACAC;IACAC;MAAA;MACA;QACA;UACA;QACA;MACA;QACAC;MACA;IACA;IACAC;MACA;MACAC;QACAC;QAAA;QACAC;UACA;YACAC;YACAC;YACAC;UACA;UACA;YACA;cACAC;cACAA;YACA;UACA;YACAR;UACA;YACAQ;UACA;QACA;MACA;IAEA;IACAC;MAAA;MACA;QACA;UACA;YACA;cACAC;cACAC;YACA;UACA;UACA;QACA;MAEA;IACA;IACAC;MAAA;MACA;QAAAC;MAAA;QACA;MACA;IACA;IACAC;MAAA;MACAZ;QACAa;QACAX;UACA;UACA;YAAAY;YAAAC;UAAA;QACA;QACAC;UACAhB;YACAiB;UACA;UACAnB;QACA;MACA;IACA;IACAoB;MAAA;MAEA;QACA;UACA;QACA;MAEA;QACApB;MACA;IACA;IACAqB;MAAA;MAAA;MACA;QACA;MACA;MACA;QACA;UAAAF;QAAA;MACA;MAEAjB;QAAAoB;MAAA;IACA;IACAC;MACA;IACA;IACAC;MACAtB;QACAoB;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACzLA;AAAA;AAAA;AAAA;AAAu9C,CAAgB,64CAAG,EAAC,C;;;;;;;;;;;ACA3+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-swiper/u-swiper\" */ \"@/uni_modules/uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-tabs/u-tabs\" */ \"@/uni_modules/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-empty/u-empty\" */ \"@/uni_modules/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.zxList.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.loginStatus = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n    <view class=\"headerBox\">\r\n    </view>\r\n\r\n    <view class=\"centerBox\">\r\n      <view class=\"item\" @click=\"handlerClick('/pages/rescue/index', false)\">\r\n        <image src=\"/static/2.png\" />\r\n        <text>道路救援</text>\r\n      </view>\r\n      <view class=\"item\" @click=\"handlerClick('/pages/serverSite/index', false)\">\r\n        <image src=\"/static/9.png\" />\r\n        <text>服务站点</text>\r\n      </view>\r\n      <view class=\"item\" @click=\"handlerClick('/pages/taskList/index', true)\">\r\n        <image src=\"/static/6.png\" />\r\n        <text>任务中心</text>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"swiperBox\">\r\n      <u-swiper :list=\"list\" circular></u-swiper>\r\n    </view>\r\n\r\n    <view class=\"newsBox\">\r\n      <u-tabs :list=\"tabsList\" @click=\"handlerCutNews\"></u-tabs>\r\n\r\n      <view class=\"newList\">\r\n        <view class=\"listItem\" v-for=\"(item) in zxList\" :key=\"item.id\" @click=\"handlerDetail(item)\">\r\n            <image class=\"listItemImg\" :src=\"item.slt\" mode=\"scaleToFill\" />\r\n            <view class=\"listItemTitle\">{{ item.title }}</view>\r\n        </view>\r\n        <u-empty v-if=\"!zxList.length\"></u-empty>\r\n      </view>\r\n    </view>\r\n\r\n    <u-popup :show=\"loginStatus\" round=\"20\" mode=\"center\" @close=\"loginStatus = false\" :safeAreaInsetBottom=\"false\">\r\n      <view class=\"popContainer\">\r\n        <view class=\"title\">手机号授权</view>\r\n        <view class=\"desc\">为了更好为您服务，请授权手机号</view>\r\n        <button class=\"btn\" type=\"primary\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\">\r\n            <text>快捷登录</text>\r\n        </button>\r\n      </view>\r\n    </u-popup>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { get } from '@/utils/request.js'\r\nimport { mapState } from 'vuex'\r\nimport uEmpty from '../../uni_modules/uview-ui/components/u-empty/u-empty.vue';\r\nexport default {\r\n  components: { uEmpty },\r\n\tdata() {\r\n\t\treturn {\r\n            list:[\r\n                'https://xdmimg.oss-cn-beijing.aliyuncs.com/%E7%B4%A0%E6%9D%90%20%281%29/%209.png',\r\n                'https://xdmimg.oss-cn-beijing.aliyuncs.com/%E7%B4%A0%E6%9D%90%20%281%29/%208.png',\r\n                'https://xdmimg.oss-cn-beijing.aliyuncs.com/%E7%B4%A0%E6%9D%90%20%281%29/%207.png'\r\n            ],\r\n            tabsList: [],\r\n            serverSiteList: [],\r\n\r\n            loginStatus: false,\r\n            zxList: [],\r\n\t\t};\r\n\t},\r\n    computed: {\r\n        ...mapState(['token', 'userInfo'])\r\n    },\r\n    onLoad(options) {\r\n        this.getZXMap()\r\n        if(this.token){\r\n            this.getUserInfo()\r\n        }\r\n    },\r\n    onShow() {\r\n        this.getCurrentLocation()\r\n        if(!this.token){\r\n            this.loginStatus = true\r\n        }\r\n    },\r\n\tonReady() {\r\n\t},\r\n\tmethods: {\r\n        getUserInfo(){\r\n            get('/system/wx/getWxInfo').then(res => {\r\n                if(res.code == '200'){\r\n                    this.$store.commit('setUserInfo', res.data)\r\n                }\r\n            }).catch(err => {\r\n                console.log(err)\r\n            })\r\n        },\r\n        getPhoneNumber(e) {\r\n            let that = this\r\n            uni.login({\r\n                provider: 'weixin', //使用微信登录\r\n                success: function (loginRes) {\r\n                    let data = {\r\n                        encryptedData: e.detail.encryptedData,\r\n                        iv: e.detail.iv,\r\n                        code: loginRes.code\r\n                    }\r\n                    get('/system/wx/user/initLogin', data).then(res => {\r\n                        if(res.code == '200'){\r\n                            that.$store.commit('setToken', res.data.token)\r\n                            that.$store.commit('setUserInfo', res.data)\r\n                        }\r\n                    }).catch(err => {\r\n                        console.log(err)\r\n                    }).finally(() => {\r\n                        that.loginStatus = false\r\n                    })\r\n                }\r\n            })\r\n            \r\n        },\r\n        getZXMap(){\r\n            get('/system/dict/data/type/sys_zx').then(res => {\r\n                if(res.code == '200'){\r\n                    this.tabsList = res.data.map(el => {\r\n                        return {\r\n                            name: el.dictLabel,\r\n                            id: el.dictValue\r\n                        }\r\n                    })\r\n                    this.getZXList(res.data[0].dictValue)\r\n                }\r\n\r\n            })\r\n        },\r\n        getZXList(type){\r\n            get('/system/wx/zxList', { zxType: type }).then(res => {\r\n                this.zxList = res.rows || []\r\n            })\r\n        },\r\n        getCurrentLocation() {\r\n            uni.getLocation({\r\n                type: 'gcj02',\r\n                success: (res) => {\r\n                    // 可以根据经纬度反向解析地址\r\n            this.getCityName({ lat: res.latitude, lng: res.longitude })\r\n                },\r\n                fail: (err) => {\r\n            uni.showToast({\r\n            title: '获取位置失败'\r\n            })\r\n                    console.log('获取位置失败:', err)\r\n                }\r\n            })\r\n        },\r\n        getCityName(data){\r\n\r\n            get('/system/wx/getCity',data).then(res => {\r\n                if(res.code == '200'){\r\n                    this.$store.commit('setCity', res.msg)\r\n                }\r\n\r\n            }).catch(err => {\r\n                console.log(err)\r\n            })\r\n        },\r\n        handlerClick(url = '', isLogin = false){\r\n            if(isLogin && !this.token){\r\n                return this.loginStatus = true\r\n            }\r\n            if(isLogin && !this.userInfo.jyId){\r\n                return uni.showToast({ title: '仅对内部人员开放' })\r\n            }\r\n\r\n            uni.navigateTo({url})\r\n        },\r\n        handlerCutNews(e){\r\n            this.getZXList(e.id)\r\n        },\r\n        handlerDetail(item){\r\n            uni.navigateTo({\r\n                url:'/pages/information/detail?id=' + item.id,\r\n            })\r\n        }\r\n\r\n    },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container{\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background-color: #ebebee;\r\n\r\n  .headerBox{\r\n    width: 100%;\r\n    height: 350rpx;\r\n    background: url('https://xdmimg.oss-cn-beijing.aliyuncs.com/%E9%A6%96%E9%A1%B5%E8%83%8C%E6%99%AF%E5%9B%BE.png') no-repeat;\r\n    background-size: 100% 100%;\r\n  }\r\n\r\n  .centerBox{\r\n    width: 700rpx;\r\n    margin: 0 auto;\r\n    padding: 25rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-around;\r\n    flex-wrap: wrap;\r\n    box-sizing: border-box;\r\n    background: #fff;\r\n    border-radius: 16rpx;\r\n    gap: 50rpx 30rpx ;\r\n\r\n    .item{\r\n      width: 20%;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      \r\n    }\r\n    .item text{\r\n      line-height: 55rpx;\r\n      font-size: 28rpx;\r\n    }\r\n    .item image{\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n    }\r\n  }\r\n\r\n  .swiperBox{\r\n    width: 700rpx;\r\n    border-radius: 16rpx;\r\n    margin: 25rpx auto;\r\n    background: #fff;\r\n  }\r\n\r\n  .newsBox{\r\n    width: 700rpx;\r\n    border-radius: 16rpx;\r\n    margin: 25rpx auto;\r\n    background: #fff;\r\n\r\n    .newList{\r\n      width: 100%;\r\n      min-height: 300rpx;\r\n      padding: 30rpx;\r\n      box-sizing: border-box;\r\n    }\r\n    .newList .listItem{\r\n        display: flex;\r\n        gap: 15rpx;\r\n        font-size: 700;\r\n        margin-bottom: 10rpx;\r\n        border-radius: 20rpx;\r\n        box-shadow: -1px 2px 9px 2px rgba(119,143,249,0.15); \r\n\r\n        .listItemImg{\r\n            width: 220rpx;\r\n            height: 150rpx;\r\n            border-radius: 20rpx;\r\n            box-sizing: border-box;\r\n            padding: 10rpx 15rpx;\r\n        }\r\n    }\r\n  }\r\n  \r\n  .popContainer{\r\n    width: 400rpx;\r\n    height: 400rpx;\r\n    padding: 30rpx;\r\n    box-sizing: border-box;\r\n    text-align: center;\r\n\r\n    .title{\r\n      font-size: 30rpx;\r\n      font-weight: bold;\r\n      margin-bottom: 40rpx;\r\n      color: #333;\r\n    }\r\n    .desc{\r\n      font-size: 28rpx;\r\n      color: #666;\r\n      margin-bottom: 70rpx\r\n    }\r\n\r\n    .btn{\r\n        height: 80rpx;\r\n        line-height: 80rpx;\r\n        padding: 0rpx 25rpx;\r\n        background: #ffa826;\r\n        color: #fff;\r\n        border-radius: 30rpx;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756731385272\n      var cssReload = require(\"D:/soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}