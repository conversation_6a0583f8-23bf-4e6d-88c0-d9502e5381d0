<view class="container data-v-57280228"><view class="headerBox data-v-57280228"></view><view class="centerBox data-v-57280228"><view data-event-opts="{{[['tap',[['handlerClick',['/pages/rescue/index',false]]]]]}}" class="item data-v-57280228" bindtap="__e"><image src="/static/2.png" class="data-v-57280228"></image><text class="data-v-57280228">道路救援</text></view><view data-event-opts="{{[['tap',[['handlerClick',['/pages/serverSite/index',false]]]]]}}" class="item data-v-57280228" bindtap="__e"><image src="/static/9.png" class="data-v-57280228"></image><text class="data-v-57280228">服务站点</text></view><view data-event-opts="{{[['tap',[['handlerClick',['/pages/taskList/index',true]]]]]}}" class="item data-v-57280228" bindtap="__e"><image src="/static/6.png" class="data-v-57280228"></image><text class="data-v-57280228">任务中心</text></view></view><view class="swiperBox data-v-57280228"><u-swiper vue-id="8dd740cc-1" list="{{list}}" circular="{{true}}" class="data-v-57280228" bind:__l="__l"></u-swiper></view><view class="newsBox data-v-57280228"><u-tabs vue-id="8dd740cc-2" list="{{tabsList}}" data-event-opts="{{[['^click',[['handlerCutNews']]]]}}" bind:click="__e" class="data-v-57280228" bind:__l="__l"></u-tabs><view class="newList data-v-57280228"><block wx:for="{{zxList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['handlerDetail',['$0'],[[['zxList','id',item.id]]]]]]]}}" class="listItem data-v-57280228" bindtap="__e"><image class="listItemImg data-v-57280228" src="{{item.slt}}" mode="scaleToFill"></image><view class="listItemTitle data-v-57280228">{{item.title}}</view></view></block><block wx:if="{{!$root.g0}}"><u-empty vue-id="8dd740cc-3" class="data-v-57280228" bind:__l="__l"></u-empty></block></view></view><u-popup vue-id="8dd740cc-4" show="{{loginStatus}}" round="20" mode="center" safeAreaInsetBottom="{{false}}" data-event-opts="{{[['^close',[['e0']]]]}}" bind:close="__e" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><view class="popContainer data-v-57280228"><view class="title data-v-57280228">手机号授权</view><view class="desc data-v-57280228">为了更好为您服务，请授权手机号</view><button class="btn data-v-57280228" type="primary" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e"><text class="data-v-57280228">快捷登录</text></button></view></u-popup></view>