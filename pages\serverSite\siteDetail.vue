<template>
  <view class="container">
    <rich-text :nodes="siteInfo"></rich-text>
  </view>
</template>

<script>
import { get } from '@/utils/request.js'
export default {
  data() {
    return {
      siteId: '',
      siteInfo: ''
    }
  },
  onLoad(options) {
    this.siteId = options.id || ''
    this.getServerSiteDetail(this.siteId)
  },
  onReady() {
  },
  methods: {
    getServerSiteDetail(id){
      get('/system/wx/getSiteInfo',{id}).then(res => {
        if(res.code == '200'){
          this.siteInfo = res.data.content
        }

      }).catch(err => {
        console.log(err)
      })
    }
  },
};
</script>


<style scoped lang="scss">
.container{
  width: 100%;
  min-height: 100vh;
  padding: 30rpx;
}
</style>