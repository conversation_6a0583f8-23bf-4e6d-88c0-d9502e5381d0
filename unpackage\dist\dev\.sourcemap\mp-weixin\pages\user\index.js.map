{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/code/work/rescue/pages/user/index.vue?04e7", "webpack:///D:/code/work/rescue/pages/user/index.vue?2b56", "webpack:///D:/code/work/rescue/pages/user/index.vue?05a4", "uni-app:///pages/user/index.vue", "webpack:///D:/code/work/rescue/pages/user/index.vue?9528", "webpack:///D:/code/work/rescue/pages/user/index.vue?22d6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "statusBarHeight", "navBarHeight", "computed", "onLoad", "uni", "url", "methods", "getUserInfo", "console", "editInfo"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqM;AACrM,gBAAgB,4MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4wB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACoChyB;AACA;AAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC,4BACA,2CACA;EACAC;IACA;MACAC;QACAC;MACA;MACA;IACA;IACA;IACA;IAEA;IACA;MACA;IACA;IAEA;EACA;EACAC;IACAC;MAAA;MACA;QACA;UACA;QACA;MACA;QACAC;MACA;IACA;IACAC;MACAL;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjFA;AAAA;AAAA;AAAA;AAAu9C,CAAgB,64CAAG,EAAC,C;;;;;;;;;;;ACA3+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=137d5072&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=137d5072&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"137d5072\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=137d5072&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <view class=\"headerBox\" :style=\"{marginTop: (statusBarHeight + navBarHeight) + 'px'}\">\r\n      <!-- <view>我的</view> -->\r\n      <!-- <u-icon name=\"setting-fill\" color=\"#666\" size=\"26\"></u-icon> -->\r\n    </view>\r\n\r\n    <view class=\"userInfo\" @click=\"editInfo\">\r\n      <view class=\"avater\">\r\n        <image :src=\"userInfo.picUrl\" />\r\n      </view>\r\n      <view class=\"name\">{{ userInfo.nickName }}</view>\r\n    </view>\r\n\r\n    <view class=\"infoDetail\">\r\n      <view class=\"item\">\r\n        <view class=\"num\">0</view>\r\n        <view class=\"text\">优惠券</view>\r\n      </view>\r\n      <view class=\"item\">\r\n        <view class=\"num\">0</view>\r\n        <view class=\"text\">粉丝</view>\r\n      </view>\r\n      <view class=\"item\">\r\n        <view class=\"num\">0</view>\r\n        <view class=\"text\">积分</view>\r\n      </view>\r\n      <view class=\"item\">\r\n        <view class=\"num\">{{ userInfo.release || 0 }}</view>\r\n        <view class=\"text\">发布</view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { get } from '@/utils/request.js'\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n  data(){\r\n    return {\r\n      statusBarHeight: 0,\r\n      navBarHeight: 0,\r\n    }\r\n  },\r\n    computed: {\r\n        ...mapState(['token', 'userInfo'])\r\n    },\r\n  onLoad(options) {\r\n    if(!this.token){\r\n        uni.reLaunch({\r\n            url:'/pages/login/index',\r\n        })\r\n        return \r\n    }\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    this.statusBarHeight = systemInfo.statusBarHeight;\r\n\r\n    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n    if (menuButtonInfo) {\r\n      this.navBarHeight = (menuButtonInfo.top - this.statusBarHeight) * 2 + menuButtonInfo.height;\r\n    }\r\n\r\n    this.getUserInfo()\r\n  },\r\n  methods: {\r\n    getUserInfo(){\r\n        get('/system/wx/getWxInfo').then(res => {\r\n            if(res.code == '200'){\r\n                this.$store.commit('setUserInfo', res.data)\r\n            }\r\n        }).catch(err => {\r\n            console.log(err)\r\n        })\r\n    },\r\n    editInfo(){\r\n      uni.navigateTo({\r\n        url:'/pages/user/editInfo',\r\n      })\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .container{\r\n    width: 100%;\r\n    height: 100vh;\r\n    padding: 0rpx 25rpx;\r\n    box-sizing: border-box;\r\n    background: linear-gradient(to bottom, #fcc7ba 0%, #ebebeb 30%);\r\n    overflow: hidden;\r\n\r\n    .headerBox{\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      font-weight: bold;\r\n      box-sizing: border-box;\r\n      margin-bottom: 30rpx;\r\n    }\r\n\r\n    .userInfo{\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 20rpx;\r\n      margin-bottom: 30rpx;\r\n\r\n      .avater, .avater image{\r\n        width: 100rpx;\r\n        height: 100rpx;\r\n        border-radius: 50%;\r\n      }\r\n      .name{\r\n        font-size: 35rpx;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n\r\n    .infoDetail{\r\n      width: 100%;\r\n      padding: 30rpx;\r\n      display: flex;\r\n      justify-content: space-around;\r\n      box-sizing: border-box;\r\n      background: #fff;\r\n      border-radius: 20rpx;\r\n\r\n      .item{\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        gap: 15rpx;\r\n      }\r\n    }\r\n  }\r\n</style>", "import mod from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=137d5072&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=137d5072&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756735408620\n      var cssReload = require(\"D:/soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}