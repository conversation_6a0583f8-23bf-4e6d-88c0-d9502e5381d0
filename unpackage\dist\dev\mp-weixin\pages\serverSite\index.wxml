<view class="container data-v-1bf96da6"><view class="searchBox data-v-1bf96da6"><view class="label data-v-1bf96da6">当前城市</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="city data-v-1bf96da6" bindtap="__e">{{''+city+''}}<u-icon vue-id="6334c820-1" name="arrow-down-fill" class="data-v-1bf96da6" bind:__l="__l"></u-icon></view></view><view class="content data-v-1bf96da6"><block wx:for="{{serverSiteList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handlerSiteDetail',['$0'],[[['serverSiteList','',index]]]]]]]}}" class="item data-v-1bf96da6" bindtap="__e"><view class="lable data-v-1bf96da6">{{item.name}}</view><u-icon vue-id="{{'6334c820-2-'+index}}" name="arrow-right" class="data-v-1bf96da6" bind:__l="__l"></u-icon></view></block><block wx:if="{{!$root.g0}}"><u-empty style="margin-top:20rpx;" vue-id="6334c820-3" class="data-v-1bf96da6" bind:__l="__l"></u-empty></block></view><u-picker vue-id="6334c820-4" show="{{cityPickerStatus}}" columns="{{[currenCityList]}}" closeOnClickOverlay="{{true}}" data-event-opts="{{[['^confirm',[['confirm']]],['^close',[['closePicker']]],['^cancel',[['closePicker']]]]}}" bind:confirm="__e" bind:close="__e" bind:cancel="__e" class="data-v-1bf96da6" bind:__l="__l"></u-picker></view>