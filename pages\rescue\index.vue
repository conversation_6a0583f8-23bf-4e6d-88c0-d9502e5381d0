<template>
	<view class="container">
    <view class="header">
      <image src="https://xdmimg.oss-cn-beijing.aliyuncs.com/%E7%B4%A0%E6%9D%90%20%281%29/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250821212300_690_528.png" mode="scaleToFill"/>
    </view>

    <view class="formBox">
		<u--form :model="form" ref="uForm" :rules="rules" errorType="toast" borderBottom labelWidth="70">
			<!-- 车型选择 -->
			<u-form-item label="车型" prop="carType" required>
				<u--input
					v-model="form.carType"
					placeholder="请选择输入车型"
				/>
			</u-form-item>
			<!-- 手机号 -->
				<u-form-item label="手机号" prop="phone" required @click="showNumberKeyboard = true">
					<u--input
						v-model="form.phone"
						placeholder="请输入手机号"
						:readonly="true"
						maxlength="11"
					/>
				</u-form-item>

				<!-- 车牌号 -->
				<u-form-item label="车牌号" prop="carNo" required @click="showLicensePlatePicker = true">
					<u-input
						v-model="form.carNo"
						placeholder="请输入车牌号"
						:readonly="true"
					/>
				</u-form-item>

				<!-- 是否有标 -->
				<u-form-item label="是否有标" prop="isTag" required>
					<u-radio-group v-model="form.isTag" placement="row">
						<u-radio label="是" name="1" style="margin-right: 30rpx;"></u-radio>
						<u-radio label="否" name="0"></u-radio>
					</u-radio-group>
				</u-form-item>

				<!-- 地址 -->
				<u-form-item label="地址" prop="address" required @click="chooseLocation">
					<u--input
						v-model="form.address"
            readonly
						placeholder="点击获取当前位置"
						suffix-icon="map"
					/>
				</u-form-item>

        <u-form-item label="详细地址" prop="detailAdd" required>
					<u--input 
            v-model="form.detailAdd"
						placeholder="点击输入详细位置"
					/>
				</u-form-item>

				<!-- 几群 -->
				<u-form-item label="几群" prop="group">
					<u--input
						v-model="form.group"
						placeholder="所在群"
						:autoHeight="true"
						maxlength="300"
					/>
				</u-form-item>

        <!-- 救援事项 -->
				<u-form-item label="救援事项" prop="desc">
					<u--input
						v-model="form.desc"
						placeholder="请描述需要救援的问题"
						type="textarea"
						:autoHeight="true"
						maxlength="200"
					/>
				</u-form-item>
			</u--form>

			<!-- 提交按钮 -->
			<view class="submit-btn-container">
				<u-button
					type="primary"
					size="large"
					:loading="submitting"
					@click="submitForm"
					customStyle="background: #2d89c5"
				>
					{{ submitting ? '提交中...' : '立即申请救援' }}
				</u-button>
			</view>
    </view>


    <!-- 车型选择器 -->
		<u-picker
			:show="showCarTypePicker"
			:columns="carTypeColumns"
			@confirm="confirmCarType"
			@cancel="showCarTypePicker = false"
		></u-picker>

		<!-- 数字键盘 -->
		<u-keyboard 
			:show="showNumberKeyboard" 
			mode="number" 
			:dotDisabled="true"
      @close="showNumberKeyboard = false"
			@change="handlerPhoneNum"
			@backspace="handlerPhoneNumBackspace"
			@cancel="showNumberKeyboard = false"
			@confirm="showNumberKeyboard = false"
		></u-keyboard>

		<!-- 车牌键盘 -->
		<u-keyboard 
			:show="showLicensePlatePicker" 
			mode="car"
      @close="showLicensePlatePicker = false"
			@change="handlerLicensePlate"
			@backspace="handlerLicensePlateBackspace"
			@cancel="showLicensePlatePicker = false"
			@confirm="showLicensePlatePicker = false"
		></u-keyboard>
	</view>
</template>

<script>
import { post } from '@/utils/request.js'
export default {
	data() {
		return {
			bgColor: 'transparent',
			// 表单数据
			form: {
				carType: '',
				phone: '',
				carNo: '',
				desc: '',
				isTag: '',
				address: '',
        detailAdd: '',
				group: '',
        longitude: '',
        latitude: ''
			},
			// 表单验证规则
			rules: {
				carType: {
						required: true,
						message: '请输入车型',
						trigger: 'change'
        },
				phone: [
					{
						required: true,
						message: '请输入手机号',
						trigger: 'change'
					},
					{
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号',
            trigger: 'change'
					}
				],
				carNo:{
          required: true,
          message: '请输入车牌号',
          trigger: 'change'
        },
				isTag:{
          required: true,
          message: '请选择是否有标',
          trigger: 'change'
        },
				address:{
          required: true,
          message: '请选择或输入地址',
          trigger: 'change'
        },
        detailAdd:{
          required: true,
          message: '请输入详细地址',
          trigger: 'change'
        }
			},
			// 车型选项
			carTypeColumns: [
				['轿车', 'SUV', '面包车', '货车', '客车', '摩托车', '电动车', '其他']
			],
			// 控制显示状态
			showCarTypePicker: false,
			showNumberKeyboard: false,
			showLicensePlatePicker: false,
			submitting: false
		};
	},
	onReady() {
			//如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
		console.log(this.rules)
    this.$refs.uForm.setRules(this.rules)
	},
	methods: {
		handlerPhoneNum(val){
			this.form.phone += val;
		},
		handlerPhoneNumBackspace(){
			if(this.form.phone.length) this.form.phone = this.form.phone.substr(0, this.form.phone.length - 1);
		},
		handlerLicensePlate(val){
			this.form.carNo += val;
		},
		handlerLicensePlateBackspace(){
			if(this.form.carNo.length) this.form.carNo = this.form.carNo.substr(0, this.form.carNo.length - 1);
		},
		// 车型选择确认
		confirmCarType(value) {
			this.form.carType = value.value[0]
			this.showCarTypePicker = false
		},

		// 选择位置
		chooseLocation() {
			uni.chooseLocation({
				success: (res) => {
          console.log(res, '获取到的位置')
					// this.form.address = res.address + ' ' + res.name
          this.form.address = res.address
          this.form.longitude = res.longitude
          this.form.latitude = res.latitude
				},
				fail: (err) => {
					// 如果用户拒绝授权，可以手动输入
					uni.showModal({
						title: '提示',
						content: '获取位置失败',
						showCancel: false
					})
				}
			})
		},

		// 获取当前位置
		getCurrentLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					// 可以根据经纬度反向解析地址
					console.log('当前位置:', res)
				},
				fail: (err) => {
					console.log('获取位置失败:', err)
				}
			})
		},

		// 表单提交
		async submitForm() {
			// 表单验证
			try {
				await this.$refs.uForm.validate()
			} catch (errors) {
				console.log('表单验证失败:', errors)
				return
			}

			this.submitting = true

			try {
				// 调用后端接口
				const response = await this.submitRescueRequest()

				uni.showToast({
					title: '提交成功,稍后会有专人电话联系您！',
					icon: 'none'
				})

				// 提交成功后可以跳转到结果页面或重置表单
				setTimeout(() => {
					this.resetForm()
				}, 1500)

			} catch (error) {
				console.error('提交失败:', error)
				uni.showToast({
					title: '提交失败，请重试',
					icon: 'none'
				})
			} finally {
				this.submitting = false
			}
		},

		// 调用后端接口提交救援请求
		async submitRescueRequest() {
			const requestData = {
				carType: this.form.carType,
				phone: this.form.phone,
				carNo: this.form.carNo,
				desc: this.form.desc,
				isTag: this.form.isTag,
				address: this.form.address,
        detailAdd: this.form.detailAdd,
				group: this.form.group,
        longitude: this.form.longitude,
        latitude: this.form.latitude
			}

			// 调用封装的post方法
			return await post('/system/wx', requestData)
		},

		// 重置表单
		resetForm() {
			this.form = {
				carType: '',
				phone: '',
				carNo: '',
				desc: '',
				isTag: '',
				address: '',
        detailAdd: '',
				group: '',
        longitude: '',
        latitude: ''
			}
			this.$refs.uForm.clearValidate()
		}
	},
};
</script>

<style scoped lang="scss">
.container{
  width: 100%;
  min-height: 100vh;
  background: #01529b;

  .header{
    width: 100%;
    height: 400rpx;

    image{
      width: 100%;
      height: 100%;
      z-index: 0;
    }
  }

  .formBox{
    width: 700rpx;
    margin: 0 auto;
    padding: 20rpx 35rpx;
    box-sizing: border-box;
    border-radius:20rpx;
    background: #fff;
    z-index: 888;
  }

  ::v-deep .u-radio{
    margin-right: 30rpx;
  }
}
</style>
