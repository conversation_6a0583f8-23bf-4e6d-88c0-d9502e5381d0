@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-69e2a36e, scroll-view.data-v-69e2a36e, swiper-item.data-v-69e2a36e {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-upload.data-v-69e2a36e {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.u-upload__wrap.data-v-69e2a36e {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  flex: 1;
}
.u-upload__wrap__preview.data-v-69e2a36e {
  border-radius: 2px;
  margin: 0 8px 8px 0;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: row;
}
.u-upload__wrap__preview__image.data-v-69e2a36e {
  width: 80px;
  height: 80px;
}
.u-upload__wrap__preview__other.data-v-69e2a36e {
  width: 80px;
  height: 80px;
  background-color: #f2f2f2;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.u-upload__wrap__preview__other__text.data-v-69e2a36e {
  font-size: 11px;
  color: #909193;
  margin-top: 2px;
}
.u-upload__deletable.data-v-69e2a36e {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #373737;
  height: 14px;
  width: 14px;
  display: flex;
  flex-direction: row;
  border-bottom-left-radius: 100px;
  align-items: center;
  justify-content: center;
  z-index: 3;
}
.u-upload__deletable__icon.data-v-69e2a36e {
  position: absolute;
  -webkit-transform: scale(0.7);
          transform: scale(0.7);
  top: 0px;
  right: 0px;
}
.u-upload__success.data-v-69e2a36e {
  position: absolute;
  bottom: 0;
  right: 0;
  display: flex;
  flex-direction: row;
  border-style: solid;
  border-top-color: transparent;
  border-left-color: transparent;
  border-bottom-color: #5ac725;
  border-right-color: #5ac725;
  border-width: 9px;
  align-items: center;
  justify-content: center;
}
.u-upload__success__icon.data-v-69e2a36e {
  position: absolute;
  -webkit-transform: scale(0.7);
          transform: scale(0.7);
  bottom: -10px;
  right: -10px;
}
.u-upload__status.data-v-69e2a36e {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.u-upload__status__icon.data-v-69e2a36e {
  position: relative;
  z-index: 1;
}
.u-upload__status__message.data-v-69e2a36e {
  font-size: 12px;
  color: #FFFFFF;
  margin-top: 5px;
}
.u-upload__button.data-v-69e2a36e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: #f4f5f7;
  border-radius: 2px;
  margin: 0 8px 8px 0;
  box-sizing: border-box;
}
.u-upload__button__text.data-v-69e2a36e {
  font-size: 11px;
  color: #909193;
  margin-top: 2px;
}
.u-upload__button--hover.data-v-69e2a36e {
  background-color: #e6e7e9;
}
.u-upload__button--disabled.data-v-69e2a36e {
  opacity: 0.5;
}

