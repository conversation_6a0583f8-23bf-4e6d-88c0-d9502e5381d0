<view class="container data-v-55e72e81"><view class="textBox data-v-55e72e81"><u-textarea bind:input="__e" vue-id="66ff22a5-1" height="150" placeholder="请输入内容" border="bottom" value="{{content}}" data-event-opts="{{[['^input',[['__set_model',['','content','$event',[]]]]]]}}" class="data-v-55e72e81" bind:__l="__l"></u-textarea></view><view hidden="{{!(type!=0)}}" class="medioBox data-v-55e72e81"><u-upload vue-id="66ff22a5-2" accept="{{type==1?'image ':'video'}}" fileList="{{fileList}}" multiple="{{type==1?true:false}}" maxCount="{{type==1?9:1}}" previewFullImage="{{true}}" data-event-opts="{{[['^afterRead',[['afterRead']]],['^delete',[['deletePic']]]]}}" bind:afterRead="__e" bind:delete="__e" class="data-v-55e72e81" bind:__l="__l"></u-upload></view><view data-event-opts="{{[['tap',[['handlerRelease',['$event']]]]]}}" class="btnBox data-v-55e72e81" bindtap="__e">发布</view></view>