<template>
	<view class="page">
		<!-- 顶部背景图 -->
		<view class="header-bg">
			<image class="bg-image" src="/static/rescue-bg.jpg" mode="aspectFill"></image>
			<view class="header-content">
				<text class="header-title">道路救援服务</text>
				<text class="header-subtitle">24小时为您提供专业救援</text>
			</view>
		</view>

		<!-- 表单内容 -->
		<view class="form-container">
			<u-form :model="form" ref="uForm" :rules="rules" :errorType="['toast']">
				<!-- 车型选择 -->
				<u-form-item label="车型" prop="carType" required>
					<u-input
						v-model="form.carType"
						placeholder="请选择车型"
						:readonly="true"
						@click="showCarTypePicker = true"
						suffix-icon="arrow-down"
					/>
				</u-form-item>

				<!-- 手机号 -->
				<u-form-item label="手机号" prop="phone" required>
					<u-input
						v-model="form.phone"
						placeholder="请输入手机号"
						@click="showNumberKeyboard = true"
						:readonly="true"
						maxlength="11"
					/>
				</u-form-item>

				<!-- 车牌号 -->
				<u-form-item label="车牌号" prop="licensePlate" required>
					<u-input
						v-model="form.licensePlate"
						placeholder="请输入车牌号"
						@click="showLicensePlatePicker = true"
						:readonly="true"
					/>
				</u-form-item>

				<!-- 救援事项 -->
				<u-form-item label="救援事项" prop="rescueItem">
					<u-input
						v-model="form.rescueItem"
						placeholder="请描述需要救援的问题"
						type="textarea"
						:autoHeight="true"
						maxlength="200"
					/>
				</u-form-item>

				<!-- 是否有标 -->
				<u-form-item label="是否有标" prop="hasSign" required>
					<u-radio-group v-model="form.hasSign" placement="row">
						<u-radio label="是" name="1"></u-radio>
						<u-radio label="否" name="0"></u-radio>
					</u-radio-group>
				</u-form-item>

				<!-- 地址 -->
				<u-form-item label="地址" prop="address" required>
					<u-input
						v-model="form.address"
						placeholder="点击获取当前位置或手动输入"
						@click="chooseLocation"
						suffix-icon="map"
					/>
				</u-form-item>

				<!-- 留言 -->
				<u-form-item label="留言" prop="message">
					<u-input
						v-model="form.message"
						placeholder="其他需要说明的情况"
						type="textarea"
						:autoHeight="true"
						maxlength="300"
					/>
				</u-form-item>
			</u-form>

			<!-- 提交按钮 -->
			<view class="submit-btn-container">
				<u-button
					type="primary"
					size="large"
					:loading="submitting"
					@click="submitForm"
					customStyle="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;"
				>
					{{ submitting ? '提交中...' : '立即申请救援' }}
				</u-button>
			</view>
		</view>

		<!-- 车型选择器 -->
		<u-picker
			:show="showCarTypePicker"
			:columns="carTypeColumns"
			@confirm="confirmCarType"
			@cancel="showCarTypePicker = false"
		></u-picker>

		<!-- 数字键盘 -->
		<u-number-keyboard
			v-model="form.phone"
			:show="showNumberKeyboard"
			mode="car"
			@close="showNumberKeyboard = false"
		></u-number-keyboard>

		<!-- 车牌键盘 -->
		<u-license-plate-keyboard
			v-model="form.licensePlate"
			:show="showLicensePlatePicker"
			@close="showLicensePlatePicker = false"
		></u-license-plate-keyboard>
	</view>
</template>

<script>
import { post } from '@/utils/request.js'

export default {
	data() {
		return {
			// 表单数据
			form: {
				carType: '',
				phone: '',
				licensePlate: '',
				rescueItem: '',
				hasSign: '',
				address: '',
				message: ''
			},
			// 表单验证规则
			rules: {
				carType: [
					{
						required: true,
						message: '请选择车型',
						trigger: 'change'
					}
				],
				phone: [
					{
						required: true,
						message: '请输入手机号',
						trigger: 'change'
					},
					{
						pattern: /^1[3-9]\d{9}$/,
						message: '请输入正确的手机号',
						trigger: 'change'
					}
				],
				licensePlate: [
					{
						required: true,
						message: '请输入车牌号',
						trigger: 'change'
					}
				],
				hasSign: [
					{
						required: true,
						message: '请选择是否有标',
						trigger: 'change'
					}
				],
				address: [
					{
						required: true,
						message: '请选择或输入地址',
						trigger: 'change'
					}
				]
			},
			// 车型选项
			carTypeColumns: [
				['轿车', 'SUV', '面包车', '货车', '客车', '摩托车', '电动车', '其他']
			],
			// 控制显示状态
			showCarTypePicker: false,
			showNumberKeyboard: false,
			showLicensePlatePicker: false,
			submitting: false
		}
	},
	onLoad() {
		// 页面加载时可以获取当前位置
		this.getCurrentLocation()
	},
	methods: {
		// 车型选择确认
		confirmCarType(value) {
			this.form.carType = value.value[0]
			this.showCarTypePicker = false
		},

		// 选择位置
		chooseLocation() {
			uni.chooseLocation({
				success: (res) => {
					this.form.address = res.address + ' ' + res.name
				},
				fail: (err) => {
					console.log('选择位置失败:', err)
					// 如果用户拒绝授权，可以手动输入
					uni.showModal({
						title: '提示',
						content: '获取位置失败，您可以手动输入地址',
						showCancel: false
					})
				}
			})
		},

		// 获取当前位置
		getCurrentLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					// 可以根据经纬度反向解析地址
					console.log('当前位置:', res)
				},
				fail: (err) => {
					console.log('获取位置失败:', err)
				}
			})
		},

		// 表单提交
		async submitForm() {
			// 表单验证
			try {
				await this.$refs.uForm.validate()
			} catch (errors) {
				console.log('表单验证失败:', errors)
				return
			}

			this.submitting = true

			try {
				// 调用后端接口
				const response = await this.submitRescueRequest()

				uni.showToast({
					title: '提交成功',
					icon: 'success'
				})

				// 提交成功后可以跳转到结果页面或重置表单
				setTimeout(() => {
					this.resetForm()
				}, 1500)

			} catch (error) {
				console.error('提交失败:', error)
				uni.showToast({
					title: '提交失败，请重试',
					icon: 'none'
				})
			} finally {
				this.submitting = false
			}
		},

		// 调用后端接口提交救援请求
		async submitRescueRequest() {
			const requestData = {
				carType: this.form.carType,
				phone: this.form.phone,
				licensePlate: this.form.licensePlate,
				rescueItem: this.form.rescueItem,
				hasSign: this.form.hasSign,
				address: this.form.address,
				message: this.form.message,
				submitTime: new Date().toISOString()
			}

			// 调用封装的post方法
			return await post('/rescue/submit', requestData)
		},

		// 重置表单
		resetForm() {
			this.form = {
				carType: '',
				phone: '',
				licensePlate: '',
				rescueItem: '',
				hasSign: '',
				address: '',
				message: ''
			}
			this.$refs.uForm.clearValidate()
		}
	}
}
</script>
<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 顶部背景区域 */
.header-bg {
	position: relative;
	height: 400rpx;
	overflow: hidden;
}

.bg-image {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
}

.header-content {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.4);
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	color: white;
}

.header-title {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.header-subtitle {
	font-size: 28rpx;
	opacity: 0.9;
	text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

/* 表单容器 */
.form-container {
	margin: -60rpx 30rpx 30rpx 30rpx;
	background: white;
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	position: relative;
	z-index: 2;
}

/* 表单项样式调整 */
.form-container :deep(.u-form-item) {
	margin-bottom: 30rpx;
}

.form-container :deep(.u-form-item__label) {
	font-weight: 600;
	color: #333;
	font-size: 32rpx;
}

.form-container :deep(.u-input__content) {
	background-color: #f8f9fa;
	border-radius: 12rpx;
	border: 2rpx solid #e9ecef;
	transition: all 0.3s ease;
}

.form-container :deep(.u-input__content:focus-within) {
	border-color: #667eea;
	background-color: white;
}

.form-container :deep(.u-textarea) {
	background-color: #f8f9fa;
	border-radius: 12rpx;
	border: 2rpx solid #e9ecef;
	min-height: 120rpx;
}

/* 单选按钮样式 */
.form-container :deep(.u-radio-group) {
	margin-top: 20rpx;
}

.form-container :deep(.u-radio) {
	margin-right: 40rpx;
}

.form-container :deep(.u-radio__icon-wrap) {
	border-color: #667eea;
}

.form-container :deep(.u-radio__icon-wrap--checked) {
	background-color: #667eea;
}

/* 提交按钮容器 */
.submit-btn-container {
	margin-top: 60rpx;
	padding: 0 20rpx;
}

.submit-btn-container :deep(.u-button) {
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 600;
	box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
}

.submit-btn-container :deep(.u-button:active) {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
	.form-container {
		margin: -40rpx 20rpx 20rpx 20rpx;
		padding: 30rpx 20rpx;
	}

	.header-title {
		font-size: 42rpx;
	}

	.header-subtitle {
		font-size: 26rpx;
	}
}

/* 动画效果 */
.form-container {
	animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
	from {
		transform: translateY(100rpx);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

/* 输入框聚焦效果 */
.form-container :deep(.u-input__content) {
	position: relative;
	overflow: hidden;
}

.form-container :deep(.u-input__content::before) {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
	transition: left 0.5s;
}

.form-container :deep(.u-input__content:focus-within::before) {
	left: 100%;
}
</style>
