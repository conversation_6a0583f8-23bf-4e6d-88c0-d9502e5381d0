<view data-event-opts="{{[['touchmove',[['noop',['$event']]]]]}}" class="u-keyboard data-v-2f1d7b83" catchtouchmove="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-keyboard__button-wrapper data-v-2f1d7b83"><view class="u-keyboard__button-wrapper__button data-v-2f1d7b83" style="{{item.s0}}" hover-class="u-hover-class" hover-stay-time="{{200}}" data-event-opts="{{[['tap',[['keyboardClick',['$0'],[[['numList','',index]]]]]]]}}" bindtap="__e"><text class="u-keyboard__button-wrapper__button__text data-v-2f1d7b83">{{item.$orig}}</text></view></view></block><view class="u-keyboard__button-wrapper data-v-2f1d7b83"><view class="u-keyboard__button-wrapper__button u-keyboard__button-wrapper__button--gray data-v-2f1d7b83" hover-class="u-hover-class" hover-stay-time="{{200}}" data-event-opts="{{[['touchstart',[['backspaceClick',['$event']]]],['touchend',[['clearTimer',['$event']]]]]}}" catchtouchstart="__e" bindtouchend="__e"><u-icon vue-id="22902495-1" name="backspace" color="#303133" size="28" class="data-v-2f1d7b83" bind:__l="__l"></u-icon></view></view></view>