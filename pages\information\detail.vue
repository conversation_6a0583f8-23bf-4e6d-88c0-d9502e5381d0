<template>
  <view class="container">
    <rich-text :nodes="detail"></rich-text>
  </view>
</template>

<script>
import { get } from '@/utils/request.js'
export default {
  data() {
    return {
      id: '',
      detail: ''
    }
  },
  onLoad(options) {
    this.id = options.id || ''
    this.getDetail(this.id)
  },
  onReady() {
  },
  methods: {
    getDetail(id){
        get('/system/wx/getZxInfo',{ id }).then(res => {
            if(res.code == '200'){
                this.detail = res.data.content
            }

        }).catch(err => {
            console.log(err)
        })
    }
  },
};
</script>


<style scoped lang="scss">
.container{
  width: 100%;
  min-height: 100vh;
  padding: 30rpx;
}
</style>