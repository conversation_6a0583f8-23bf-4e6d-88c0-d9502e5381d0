<view class="container data-v-e17fac1a"><view class="content data-v-e17fac1a"><view class="item data-v-e17fac1a"><view class="label data-v-e17fac1a">头像</view><view class="value data-v-e17fac1a"><button class="avatar data-v-e17fac1a" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['chooseavatar',['$event']]]]]}}" bindchooseavatar="__e"><image src="https://cbu01.alicdn.com/img/ibank/O1CN01xHYrOo1Bs2ywlIm2k_!!0-0-cib.jpg" class="data-v-e17fac1a"></image></button><u-icon vue-id="58c59573-1" name="arrow-right" color="#999" size="15" class="data-v-e17fac1a" bind:__l="__l"></u-icon></view></view><view class="item data-v-e17fac1a"><view class="label data-v-e17fac1a">昵称</view><view class="value data-v-e17fac1a"><input type="text" value="********" disabled="{{!editStatus}}" class="data-v-e17fac1a"/></view></view><view class="item data-v-e17fac1a"><view class="label data-v-e17fac1a">性别</view><view data-event-opts="{{[['tap',[['slectSex',['$event']]]]]}}" class="value data-v-e17fac1a" bindtap="__e"><view class="data-v-e17fac1a">男</view><u-icon vue-id="58c59573-2" name="arrow-right" color="#999" size="15" class="data-v-e17fac1a" bind:__l="__l"></u-icon></view></view></view><block wx:if="{{!editStatus}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="btn data-v-e17fac1a" bindtap="__e">编辑</view></block><block wx:if="{{editStatus}}"><view data-event-opts="{{[['tap',[['cancle',['$event']]]]]}}" class="btn btn2 data-v-e17fac1a" bindtap="__e">取消</view></block><block wx:if="{{editStatus}}"><view class="btn btn3 data-v-e17fac1a">保存</view></block><u-action-sheet vue-id="58c59573-3" actions="{{list}}" show="{{sexStatus}}" cancelText="取消" data-event-opts="{{[['^select',[['selectClick']]],['^close',[['e1']]]]}}" bind:select="__e" bind:close="__e" class="data-v-e17fac1a" bind:__l="__l"></u-action-sheet></view>