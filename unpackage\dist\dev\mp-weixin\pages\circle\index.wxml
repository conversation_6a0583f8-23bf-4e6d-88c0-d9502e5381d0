<view class="container data-v-92537b68"><view class="navbarBox data-v-92537b68" style="{{'margin-top:'+(statusBarHeight+'px')+';'+('height:'+(navBarHeight+'px')+';')}}"><view data-event-opts="{{[['tap',[['showTypePopup',['$event']]]]]}}" class="release data-v-92537b68" bindtap="__e"><u-icon vue-id="23f2883a-1" name="plus" color="#44d4a7" class="data-v-92537b68" bind:__l="__l"></u-icon>发布</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="city data-v-92537b68" bindtap="__e">{{''+city+''}}<u-icon vue-id="23f2883a-2" name="arrow-down-fill" color="#000" class="data-v-92537b68" bind:__l="__l"></u-icon></view></view><scroll-view class="scrollBox data-v-92537b68" style="{{'height:'+('calc(100vh - '+statusBarHeight+'px - '+navBarHeight+'px)')+';'}}" scroll-y="true" refresher-enabled="{{true}}" refresher-triggered="{{refresherTriggered}}" refresher-threshold="100" lower-threshold="100" data-event-opts="{{[['refresherpulling',[['handlerRefresher',['$event']]]],['scrolltolower',[['handlerTolower',['$event']]]]]}}" bindrefresherpulling="__e" bindscrolltolower="__e"><view class="contentBox data-v-92537b68"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['postsList','id',item.$orig.id]]]]]]]}}" class="item data-v-92537b68" bindtap="__e"><view class="header data-v-92537b68"><view class="left data-v-92537b68"><image class="avater data-v-92537b68" src="{{item.$orig.picUrl}}"></image><view class="info data-v-92537b68"><view class="name data-v-92537b68">{{item.$orig.nickName}}</view><view class="time data-v-92537b68">{{item.$orig.createdAt}}</view></view></view><view data-event-opts="{{[['tap',[['handlerDelete',['$0'],[[['postsList','id',item.$orig.id]]]]]]]}}" hidden="{{!(userInfo.id==item.$orig.userId)}}" catchtap="__e" class="data-v-92537b68"><u-icon class="right data-v-92537b68" vue-id="{{'23f2883a-3-'+index}}" name="trash-fill" size="23" color="#999" bind:__l="__l"></u-icon></view></view><view class="content data-v-92537b68"><view class="textBox data-v-92537b68">{{item.$orig.content}}</view><block wx:if="{{item.$orig.mediaType==1}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="mideo data-v-92537b68" catchtap="__e"><block wx:for="{{item.$orig.postMediaList}}" wx:for-item="el" wx:for-index="idx" wx:key="idx"><image lazy-load="{{true}}" src="{{el.mediaUrl}}" mode="widthFix" data-event-opts="{{[['tap',[['preViewImg',['$0'],[[['postsList','id',item.$orig.id],['postMediaList','',idx,'mediaUrl']]]]]]]}}" bindtap="__e" class="data-v-92537b68"></image></block></view></block><block wx:else><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="mideo data-v-92537b68" catchtap="__e"><video src="{{item.$orig.postMediaList[0].mediaUrl}}" enable-danmu="{{true}}" danmu-btn="{{true}}" controls="{{true}}" class="data-v-92537b68"></video></view></block></view><view class="bottomBtnBox data-v-92537b68"><view class="cityName data-v-92537b68">{{item.$orig.city}}</view><view class="bottomBtn data-v-92537b68"><view class="iconBox data-v-92537b68"><button class="shareBtn data-v-92537b68" open-type="share" data-event-opts="{{[['tap',[['handlerClick',['share','$0',index],[[['postsList','id',item.$orig.id]]]]]]]}}" catchtap="__e"><u-icon vue-id="{{'23f2883a-4-'+index}}" name="share-square" color="#999" size="23" class="data-v-92537b68" bind:__l="__l"></u-icon><text class="data-v-92537b68">{{item.$orig.fxCount}}</text></button></view><view class="iconBox data-v-92537b68"><u-icon vue-id="{{'23f2883a-5-'+index}}" name="file-text" color="#999" size="23" class="data-v-92537b68" bind:__l="__l"></u-icon><text class="data-v-92537b68">{{item.g0}}</text></view><view data-event-opts="{{[['tap',[['handlerClick',['thumbUp','$0',index],[[['postsList','id',item.$orig.id]]]]]]]}}" class="iconBox data-v-92537b68" catchtap="__e"><u-icon vue-id="{{'23f2883a-6-'+index}}" name="thumb-up" color="{{item.$orig.isLikes?'#feae61':'#999'}}" size="23" class="data-v-92537b68" bind:__l="__l"></u-icon><text class="data-v-92537b68">{{item.$orig.likeCount}}</text></view></view></view></view></block></view><block wx:if="{{$root.g1==0}}"><view class="data-v-92537b68"><u-empty vue-id="23f2883a-7" class="data-v-92537b68" bind:__l="__l"></u-empty></view></block></scroll-view><u-popup vue-id="23f2883a-8" show="{{releaseStatus}}" mode="center" round="10" safeAreaInsetBottom="{{false}}" data-event-opts="{{[['^close',[['e1']]]]}}" bind:close="__e" class="data-v-92537b68" bind:__l="__l" vue-slots="{{['default']}}"><view class="releaseList data-v-92537b68"><view data-event-opts="{{[['tap',[['handlerToRelease',[0]]]]]}}" class="item data-v-92537b68" catchtap="__e"><image src="/static/text.png" class="data-v-92537b68"></image><text class="data-v-92537b68">写心情</text></view><view data-event-opts="{{[['tap',[['handlerToRelease',[1]]]]]}}" class="item data-v-92537b68" catchtap="__e"><image src="/static/picture.png" class="data-v-92537b68"></image><text class="data-v-92537b68">图片</text></view><view data-event-opts="{{[['tap',[['handlerToRelease',[2]]]]]}}" class="item data-v-92537b68" catchtap="__e"><image src="/static/video.png" class="data-v-92537b68"></image><text class="data-v-92537b68">视频</text></view></view></u-popup><u-picker vue-id="23f2883a-9" show="{{cityPickerStatus}}" columns="{{[currenCityList]}}" closeOnClickOverlay="{{true}}" data-event-opts="{{[['^confirm',[['confirm']]],['^close',[['e2']]],['^cancel',[['e3']]]]}}" bind:confirm="__e" bind:close="__e" bind:cancel="__e" class="data-v-92537b68" bind:__l="__l"></u-picker><u-action-sheet vue-id="23f2883a-10" actions="{{list}}" show="{{moreStatus}}" cancelText="取消" data-event-opts="{{[['^select',[['moreSheetClick']]],['^close',[['e4']]]]}}" bind:select="__e" bind:close="__e" class="data-v-92537b68" bind:__l="__l"></u-action-sheet></view>