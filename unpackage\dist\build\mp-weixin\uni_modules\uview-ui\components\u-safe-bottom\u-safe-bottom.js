(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom"],{4700:function(t,e,n){"use strict";n.d(e,"b",(function(){return u})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var u=function(){var t=this.$createElement,e=(this._self._c,this.__get_style([this.style]));this.$mp.data=Object.assign({},{$root:{s0:e}})},i=[]},"5b4e":function(t,e,n){"use strict";n.r(e);var u=n("7005"),i=n.n(u);for(var o in u)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return u[t]}))}(o);e["default"]=i.a},"699c":function(t,e,n){},7005:function(t,e,n){"use strict";(function(t){var u=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=u(n("0ee1")),o={name:"u-safe-bottom",mixins:[t.$u.mpMixin,t.$u.mixin,i.default],data:function(){return{safeAreaBottomHeight:0,isNvue:!1}},computed:{style:function(){return t.$u.deepMerge({},t.$u.addStyle(this.customStyle))}},mounted:function(){}};e.default=o}).call(this,n("df3c")["default"])},8072:function(t,e,n){"use strict";n.r(e);var u=n("4700"),i=n("5b4e");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("e061");var a=n("828b"),c=Object(a["a"])(i["default"],u["b"],u["c"],!1,null,"01127184",null,!1,u["a"],void 0);e["default"]=c.exports},e061:function(t,e,n){"use strict";var u=n("699c"),i=n.n(u);i.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom-create-component',
    {
        'uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("8072"))
        })
    },
    [['uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom-create-component']]
]);
