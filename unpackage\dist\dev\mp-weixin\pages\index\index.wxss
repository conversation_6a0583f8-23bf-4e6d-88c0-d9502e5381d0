@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-57280228 {
  width: 100%;
  min-height: 100vh;
  background-color: #ebebee;
}
.container .headerBox.data-v-57280228 {
  width: 100%;
  height: 350rpx;
  background: url("https://xdmimg.oss-cn-beijing.aliyuncs.com/%E9%A6%96%E9%A1%B5%E8%83%8C%E6%99%AF%E5%9B%BE.png") no-repeat;
  background-size: 100% 100%;
}
.container .centerBox.data-v-57280228 {
  width: 700rpx;
  margin: 0 auto;
  padding: 25rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-wrap: wrap;
  box-sizing: border-box;
  background: #fff;
  border-radius: 16rpx;
  gap: 50rpx 30rpx;
}
.container .centerBox .item.data-v-57280228 {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.container .centerBox .item text.data-v-57280228 {
  line-height: 55rpx;
  font-size: 28rpx;
}
.container .centerBox .item image.data-v-57280228 {
  width: 60rpx;
  height: 60rpx;
}
.container .swiperBox.data-v-57280228 {
  width: 700rpx;
  border-radius: 16rpx;
  margin: 25rpx auto;
  background: #fff;
}
.container .newsBox.data-v-57280228 {
  width: 700rpx;
  border-radius: 16rpx;
  margin: 25rpx auto;
  background: #fff;
}
.container .newsBox .newList.data-v-57280228 {
  width: 100%;
  min-height: 300rpx;
  padding: 30rpx;
  box-sizing: border-box;
}
.container .newsBox .newList .listItem.data-v-57280228 {
  display: flex;
  gap: 15rpx;
  font-size: 700;
  margin-bottom: 10rpx;
  border-radius: 20rpx;
  box-shadow: -1px 2px 9px 2px rgba(119, 143, 249, 0.15);
}
.container .newsBox .newList .listItem .listItemImg.data-v-57280228 {
  width: 220rpx;
  height: 150rpx;
  border-radius: 20rpx;
  box-sizing: border-box;
  padding: 10rpx 15rpx;
}
.container .popContainer.data-v-57280228 {
  width: 400rpx;
  height: 400rpx;
  padding: 30rpx;
  box-sizing: border-box;
  text-align: center;
}
.container .popContainer .title.data-v-57280228 {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  color: #333;
}
.container .popContainer .desc.data-v-57280228 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 70rpx;
}
.container .popContainer .btn.data-v-57280228 {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0rpx 25rpx;
  background: #ffa826;
  color: #fff;
  border-radius: 30rpx;
}

