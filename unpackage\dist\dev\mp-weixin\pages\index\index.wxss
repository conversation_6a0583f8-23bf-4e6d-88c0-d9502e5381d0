
.page.data-v-57280228 {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 顶部背景区域 */
.header-bg.data-v-57280228 {
	position: relative;
	height: 400rpx;
	overflow: hidden;
}
.bg-image.data-v-57280228 {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
}
.header-content.data-v-57280228 {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.4);
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	color: white;
}
.header-title.data-v-57280228 {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
}
.header-subtitle.data-v-57280228 {
	font-size: 28rpx;
	opacity: 0.9;
	text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

/* 表单容器 */
.form-container.data-v-57280228 {
	margin: -60rpx 30rpx 30rpx 30rpx;
	background: white;
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	position: relative;
	z-index: 2;
}

/* 表单项样式调整 */
.form-container.data-v-57280228 :deep(.u-form-item) {
	margin-bottom: 30rpx;
}
.form-container.data-v-57280228 :deep(.u-form-item__label) {
	font-weight: 600;
	color: #333;
	font-size: 32rpx;
}
.form-container.data-v-57280228 :deep(.u-input__content) {
	background-color: #f8f9fa;
	border-radius: 12rpx;
	border: 2rpx solid #e9ecef;
	transition: all 0.3s ease;
}
.form-container.data-v-57280228 :deep(.u-input__content:focus-within) {
	border-color: #667eea;
	background-color: white;
}
.form-container.data-v-57280228 :deep(.u-textarea) {
	background-color: #f8f9fa;
	border-radius: 12rpx;
	border: 2rpx solid #e9ecef;
	min-height: 120rpx;
}

/* 单选按钮样式 */
.form-container.data-v-57280228 :deep(.u-radio-group) {
	margin-top: 20rpx;
}
.form-container.data-v-57280228 :deep(.u-radio) {
	margin-right: 40rpx;
}
.form-container.data-v-57280228 :deep(.u-radio__icon-wrap) {
	border-color: #667eea;
}
.form-container.data-v-57280228 :deep(.u-radio__icon-wrap--checked) {
	background-color: #667eea;
}

/* 提交按钮容器 */
.submit-btn-container.data-v-57280228 {
	margin-top: 60rpx;
	padding: 0 20rpx;
}
.submit-btn-container.data-v-57280228 :deep(.u-button) {
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 600;
	box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
}
.submit-btn-container.data-v-57280228 :deep(.u-button:active) {
	-webkit-transform: translateY(2rpx);
	        transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
.form-container.data-v-57280228 {
		margin: -40rpx 20rpx 20rpx 20rpx;
		padding: 30rpx 20rpx;
}
.header-title.data-v-57280228 {
		font-size: 42rpx;
}
.header-subtitle.data-v-57280228 {
		font-size: 26rpx;
}
}

/* 动画效果 */
.form-container.data-v-57280228 {
	-webkit-animation: slideUp-data-v-57280228 0.6s ease-out;
	        animation: slideUp-data-v-57280228 0.6s ease-out;
}
@-webkit-keyframes slideUp-data-v-57280228 {
from {
		-webkit-transform: translateY(100rpx);
		        transform: translateY(100rpx);
		opacity: 0;
}
to {
		-webkit-transform: translateY(0);
		        transform: translateY(0);
		opacity: 1;
}
}
@keyframes slideUp-data-v-57280228 {
from {
		-webkit-transform: translateY(100rpx);
		        transform: translateY(100rpx);
		opacity: 0;
}
to {
		-webkit-transform: translateY(0);
		        transform: translateY(0);
		opacity: 1;
}
}

/* 输入框聚焦效果 */
.form-container.data-v-57280228 :deep(.u-input__content) {
	position: relative;
	overflow: hidden;
}
.form-container.data-v-57280228 :deep(.u-input__content::before) {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
	transition: left 0.5s;
}
.form-container.data-v-57280228 :deep(.u-input__content:focus-within::before) {
	left: 100%;
}

