@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-e17fac1a {
  width: 100%;
  height: 100vh;
  padding: 0 30rpx;
  background: #f5f5f5;
  overflow: hidden;
  box-sizing: border-box;
}
.container .content .item.data-v-e17fac1a {
  width: 100%;
  height: 100rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #e0e0e0;
  color: #666;
}
.container .content .item .value.data-v-e17fac1a {
  display: flex;
  align-items: center;
}
.container .content .item .value input.data-v-e17fac1a {
  text-align: right;
}
.container .content .item .value .avatar.data-v-e17fac1a {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  padding: 0rpx;
  line-height: 0rpx;
}
.container .content .item .value .avatar image.data-v-e17fac1a {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
}
.container .btn.data-v-e17fac1a {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20rpx;
  color: #fff;
  background: #71b168;
  margin-top: 50rpx;
}
.container .btn2.data-v-e17fac1a {
  background: #cccac7;
}
.container .btn3.data-v-e17fac1a {
  background: #ff9900;
}

