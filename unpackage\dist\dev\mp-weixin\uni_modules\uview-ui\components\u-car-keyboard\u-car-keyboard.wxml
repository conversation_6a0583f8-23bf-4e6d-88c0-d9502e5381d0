<view data-event-opts="{{[['touchmove',[['noop',['$event']]]]]}}" class="u-keyboard data-v-7d4e74e9" catchtouchmove="__e"><block wx:for="{{abc?engKeyBoardList:areaList}}" wx:for-item="group" wx:for-index="i" wx:key="i"><view class="{{['u-keyboard__button','data-v-7d4e74e9',i+1===4&&'u-keyboard__button--center']}}" index="{{i}}"><block wx:if="{{i===3}}"><view class="u-keyboard__button__inner-wrapper data-v-7d4e74e9"><view class="u-keyboard__button__inner-wrapper__left data-v-7d4e74e9" hover-class="u-hover-class" hover-stay-time="{{200}}" data-event-opts="{{[['tap',[['changeCarInputMode',['$event']]]]]}}" bindtap="__e"><text class="{{['u-keyboard__button__inner-wrapper__left__lang','data-v-7d4e74e9',!abc&&'u-keyboard__button__inner-wrapper__left__lang--active']}}">中</text><text class="u-keyboard__button__inner-wrapper__left__line data-v-7d4e74e9">/</text><text class="{{['u-keyboard__button__inner-wrapper__left__lang','data-v-7d4e74e9',abc&&'u-keyboard__button__inner-wrapper__left__lang--active']}}">英</text></view></view></block><block wx:for="{{group}}" wx:for-item="item" wx:for-index="j" wx:key="j"><view class="u-keyboard__button__inner-wrapper data-v-7d4e74e9"><view class="u-keyboard__button__inner-wrapper__inner data-v-7d4e74e9" hover-stay-time="{{200}}" hover-class="u-hover-class" data-event-opts="{{[['tap',[['carInputClick',[i,j]]]]]}}" bindtap="__e"><text class="u-keyboard__button__inner-wrapper__inner__text data-v-7d4e74e9">{{item}}</text></view></view></block><block wx:if="{{i===3}}"><view data-event-opts="{{[['touchstart',[['backspaceClick',['$event']]]],['touchend',[['clearTimer',['$event']]]]]}}" class="u-keyboard__button__inner-wrapper data-v-7d4e74e9" bindtouchstart="__e" bindtouchend="__e"><view class="u-keyboard__button__inner-wrapper__right data-v-7d4e74e9" hover-class="u-hover-class" hover-stay-time="{{200}}"><u-icon vue-id="{{'2c80a93d-1-'+i}}" size="28" name="backspace" color="#303133" class="data-v-7d4e74e9" bind:__l="__l"></u-icon></view></view></block></view></block></view>