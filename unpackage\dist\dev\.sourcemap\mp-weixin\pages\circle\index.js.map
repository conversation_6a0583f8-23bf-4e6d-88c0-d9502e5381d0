{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/code/work/rescue/pages/circle/index.vue?1d37", "webpack:///D:/code/work/rescue/pages/circle/index.vue?11d3", "webpack:///D:/code/work/rescue/pages/circle/index.vue?c2ae", "webpack:///D:/code/work/rescue/pages/circle/index.vue?6e69", "uni-app:///pages/circle/index.vue", "webpack:///D:/code/work/rescue/pages/circle/index.vue?86ad", "webpack:///D:/code/work/rescue/pages/circle/index.vue?c880"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "statusBarHeight", "navBarHeight", "releaseStatus", "cityPickerStatus", "postsList", "moreStatus", "moreList", "name", "params", "pageNum", "pageSize", "refresherTriggered", "city", "currenCityList", "dataEnd", "computed", "onLoad", "onShow", "methods", "preViewImg", "uni", "urls", "getCityList", "getPostsList", "title", "icon", "setTimeout", "showTypePopup", "handler<PERSON>ef<PERSON>er", "handlerTolower", "handlerToRelease", "url", "toDetail", "confirm", "handlerClick", "postId", "content", "slt", "mediaType", "postMediaList", "like", "sharePosts", "shareFn", "provider", "scene", "type", "href", "summary", "imageUrl", "success", "console", "fail", "thumbUpPosts", "handlerDelete", "that"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqM;AACrM,gBAAgB,4MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,iVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxFA;AAAA;AAAA;AAAA;AAA4wB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACoGhyB;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MAEAC;MACAC;QACAC;MACA;MAEAC;QACAC;QACAC;MACA;MAEAC;MAEAC;MACAC;MACAC;IACA;EACA;EACAC,4BACA,8CACA;EACAC;IACA;IACA;IAEA;IACA;MACA;IACA;IACA;MACA;IACA;IAEA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;YAAA;UAAA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACAH;QACAI;QACAC;MACA;MACA;QACA;UACA;QACA;UACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;QACAC;UACA;QACA;QACAN;MACA;IACA;IACAO;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MACAV;QACAW;MACA;IACA;IACAC;MACAZ;QACAW;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;UACA1B;YACA2B;YACAC;YACAC;YACAC;YACAC;UACA;UACA;UACA;QACA;UACA/B;YACA2B;YACAK;UACA;UACA;UACA;QACA;UACA;MAAA;IAEA;IACAC;MAAA;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;MACA;MACAtB;QACAuB;QACAC;QACAC;QACAC;QACAtB;QACAuB;QACAC;QACAC;UACAC;QACA;QACAC;UACAD;QACA;MACA;IACA;IACAE;MAAA;MACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACAjC;QACAI;QACAY;QACAa;UACA;YACA;cACA7B;gBAAAI;gBAAAC;cAAA;cACA6B;cACAA;YACA;UACA;YACAJ;UACA;QACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACnTA;AAAA;AAAA;AAAA;AAAu9C,CAAgB,64CAAG,EAAC,C;;;;;;;;;;;ACA3+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/circle/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/circle/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=92537b68&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=92537b68&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"92537b68\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/circle/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=92537b68&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-empty/u-empty\" */ \"@/uni_modules/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-picker/u-picker\" */ \"@/uni_modules/uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uActionSheet: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-action-sheet/u-action-sheet\" */ \"@/uni_modules/uview-ui/components/u-action-sheet/u-action-sheet.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.postsList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.comments.length\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  var g1 = _vm.postsList.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.cityPickerStatus = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.releaseStatus = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.cityPickerStatus = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.cityPickerStatus = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.moreStatus = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <view class=\"navbarBox\" :style=\"{\r\n        marginTop: statusBarHeight + 'px',\r\n        height: navBarHeight + 'px',\r\n      }\">\r\n      <view class=\"release\" @click=\"showTypePopup\">\r\n        <u-icon name=\"plus\" color=\"#44d4a7\"></u-icon>\r\n        发布\r\n      </view>\r\n      <view class=\"city\" @click=\"cityPickerStatus = true\">\r\n        {{ city }}\r\n        <u-icon name=\"arrow-down-fill\" color=\"#000\"></u-icon>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view \r\n        scroll-y=\"true\" \r\n        class=\"scrollBox\" \r\n        refresher-enabled\r\n        :refresher-triggered=\"refresherTriggered\"\r\n        @refresherpulling=\"handlerRefresher\" \r\n        refresher-threshold=\"100\"\r\n        @scrolltolower=\"handlerTolower\"\r\n        lower-threshold=\"100\" :style=\"{ height: `calc(100vh - ${statusBarHeight}px - ${navBarHeight}px)` }\"\r\n    >\r\n      <view class=\"contentBox\">\r\n        <view class=\"item\" v-for=\"(item, index) in postsList\" :key=\"item.id\" @click=\"toDetail(item)\">\r\n          <view class=\"header\">\r\n            <view class=\"left\">\r\n              <image class=\"avater\" :src=\"item.picUrl\" />\r\n              <view class=\"info\">\r\n                <view class=\"name\">{{ item.nickName }}</view>\r\n                <view class=\"time\">{{ item.createdAt }}</view>\r\n              </view>\r\n            </view>\r\n            <view @click.stop=\"handlerDelete(item)\" v-show=\"userInfo.id == item.userId\">\r\n                <u-icon class=\"right\" name=\"trash-fill\" size=\"23\" color=\"#999\"></u-icon>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"content\">\r\n            <view class=\"textBox\">{{ item.content }}</view>\r\n            <view class=\"mideo\" v-if=\"item.mediaType == 1\" @click.stop>\r\n              <image v-for=\"(el,idx) in item.postMediaList\" :key=\"idx\" lazy-load :src=\"el.mediaUrl\" mode=\"widthFix\" @click=\"preViewImg(el.mediaUrl)\"/>\r\n            </view>\r\n            <view class=\"mideo\" v-else @click.stop>\r\n                <video :src=\"item.postMediaList[0].mediaUrl\" enable-danmu danmu-btn controls></video>\r\n            </view>\r\n          </view>\r\n\r\n            <view class=\"bottomBtnBox\">\r\n                <view class=\"cityName\">{{ item.city }}</view>\r\n                <view class=\"bottomBtn\">\r\n                    <view class=\"iconBox\">\r\n                        <button class=\"shareBtn\" open-type=\"share\" @click.stop=\"handlerClick('share', item, index)\">\r\n                            <u-icon name=\"share-square\" color=\"#999\" size=\"23\"></u-icon>\r\n                            <text>{{ item.fxCount }}</text>\r\n                        </button>\r\n                    </view>\r\n                    <view class=\"iconBox\">\r\n                        <u-icon name=\"file-text\" color=\"#999\" size=\"23\"></u-icon>\r\n                        <text>{{ item.comments.length }}</text>\r\n                    </view>\r\n                    <view class=\"iconBox\" @click.stop=\"handlerClick('thumbUp', item, index)\">\r\n                        <u-icon name=\"thumb-up\" :color=\"item.isLikes? '#feae61': '#999'\" size=\"23\"></u-icon>\r\n                        <text>{{ item.likeCount }}</text>\r\n                    </view>\r\n                </view>\r\n            </view>\r\n        </view>\r\n      </view>\r\n      <view v-if=\"postsList.length == 0\">\r\n        <u-empty></u-empty>\r\n      </view>\r\n    </scroll-view>\r\n\r\n    <u-popup :show=\"releaseStatus\" mode=\"center\" round=\"10\" :safeAreaInsetBottom=\"false\" @close=\"releaseStatus = false\">\r\n      <view class=\"releaseList\">\r\n        <view class=\"item\" @click.stop=\"handlerToRelease(0)\">\r\n          <image src=\"/static/text.png\" />\r\n          <text>写心情</text>\r\n        </view>\r\n        <view class=\"item\" @click.stop=\"handlerToRelease(1)\">\r\n          <image src=\"/static/picture.png\" />\r\n          <text>图片</text>\r\n        </view>\r\n        <view class=\"item\" @click.stop=\"handlerToRelease(2)\">\r\n          <image src=\"/static/video.png\" />\r\n          <text>视频</text>\r\n        </view>\r\n      </view>\r\n    </u-popup>\r\n\r\n    <u-picker :show=\"cityPickerStatus\" :columns=\"[currenCityList]\" @confirm=\"confirm\" @close=\"cityPickerStatus = false\" @cancel=\"cityPickerStatus = false\" closeOnClickOverlay></u-picker>\r\n    <u-action-sheet :actions=\"list\" :show=\"moreStatus\" cancelText=\"取消\"  @select=\"moreSheetClick\" @close=\"moreStatus = false\"></u-action-sheet>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport { get, post, del } from '@/utils/request.js'\r\n\r\nexport default {\r\n    data () {\r\n        return {\r\n            statusBarHeight: 0,\r\n            navBarHeight: 0,\r\n            releaseStatus: false,\r\n            cityPickerStatus: false,\r\n\r\n            postsList: [],\r\n\r\n            moreStatus: false,\r\n            moreList: [{\r\n                name:'删除'\r\n            }],\r\n\r\n            params: {\r\n                pageNum: 1,\r\n                pageSize: 20\r\n            },\r\n\r\n            refresherTriggered: false,\r\n\r\n            city: '全部',\r\n            currenCityList: [],\r\n            dataEnd: true\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapState(['cityList', 'userInfo']),\r\n    },\r\n    onLoad (options) {\r\n        const systemInfo = uni.getSystemInfoSync()\r\n        this.statusBarHeight = systemInfo.statusBarHeight\r\n\r\n        const menuButtonInfo = uni.getMenuButtonBoundingClientRect()\r\n        if (menuButtonInfo) {\r\n            this.navBarHeight = (menuButtonInfo.top - this.statusBarHeight) * 2 + menuButtonInfo.height\r\n        }\r\n        if (!this.cityList.length) {\r\n            this.getCityList()\r\n        }\r\n\r\n        this.currenCityList = this.cityList\r\n        this.currenCityList.unshift('全部')\r\n    },\r\n    onShow(){\r\n        this.getPostsList()\r\n    },\r\n    methods: {\r\n        preViewImg(url){\r\n            uni.previewImage({\r\n                urls: [url]\r\n            })\r\n        },\r\n        getCityList () {\r\n            get('/system/dict/data/type/sys_city').then((res) => {\r\n                if (res.code == '200') {\r\n                    let _cityList = res.data.map((el) => el.dictLabel)\r\n                    this.$store.commit('setCityList', _cityList)\r\n                }\r\n            })\r\n        },\r\n        getPostsList(){\r\n            uni.showLoading({\r\n                title: '加载中...',\r\n                icon: 'none'\r\n            })\r\n            get('/system/wx/posts/list', this.params).then((res) => {\r\n                if (this.params.pageNum == 1) {\r\n                    this.postsList = res.rows || []\r\n                }else{\r\n                    this.postsList = this.postsList.concat(res.rows || [])\r\n                }\r\n                if(res.rows.length < 20){\r\n                    this.dataEnd = true\r\n                }else{\r\n                    this.dataEnd = false\r\n                }\r\n            }).finally(() => {\r\n                setTimeout(() => {\r\n                    this.refresherTriggered = false\r\n                }, 1000);\r\n                uni.hideLoading()\r\n            })\r\n        },\r\n        showTypePopup () {\r\n            this.releaseStatus = true\r\n        },\r\n        handlerRefresher () { \r\n            if(this.refresherTriggered) return\r\n            this.refresherTriggered = true\r\n            this.params.pageNum = 1\r\n            this.getPostsList()\r\n        },\r\n        handlerTolower () {\r\n            if(!this.dataEnd){\r\n                this.params.pageNum += 1\r\n                this.getPostsList()\r\n            }\r\n         },\r\n\r\n        handlerToRelease (type) {\r\n            this.releaseStatus = false\r\n            uni.navigateTo({\r\n                url: '/pages/circle/release?type=' + type,\r\n            })\r\n        },\r\n        toDetail (item) {\r\n            uni.navigateTo({\r\n                url: '/pages/circle/detail?id=' + item.id,\r\n            })\r\n        },\r\n        confirm(e){\r\n            this.city = e.value[0]\r\n            this.cityPickerStatus = false\r\n            this.params.pageNum = 1\r\n            this.params.city = this.city\r\n            if(this.city == '全部'){\r\n                delete this.params.city\r\n            }\r\n            this.getPostsList()\r\n        },\r\n        handlerClick(type, data, index){\r\n            let params = {}\r\n            switch (type) {\r\n                case 'share':\r\n                    params = {\r\n                        postId: data.id,\r\n                        content: data.content,\r\n                        slt: data.slt,\r\n                        mediaType: data.mediaType,\r\n                        postMediaList: data.postMediaList\r\n                    }\r\n                    this.sharePosts(params, index)\r\n                    break;\r\n                case 'thumbUp':\r\n                     params = {\r\n                        postId: data.id,\r\n                        like: data.isLikes? 0 : 1\r\n                    }\r\n                    this.thumbUpPosts(params, index)\r\n                    break;\r\n                default:\r\n                    break;\r\n            }\r\n        },\r\n        sharePosts(data, index){\r\n            post('/system/wx/posts/fx',data).then((res) => {\r\n                if (res.code == '200') {\r\n                    this.$set(this.postsList[index], 'fxCount', this.postsList[index].fxCount + 1)\r\n                    this.shareFn(data)\r\n                }\r\n            })\r\n        },\r\n        shareFn(data){\r\n            let typeMap = {\r\n                0: 1,\r\n                1: 0,\r\n                2: 4\r\n            }\r\n            uni.share({\r\n                provider: \"weixin\",\r\n                scene: \"WXSceneSession\",\r\n                type: typeMap[data.mediaType],\r\n                href: \"/pages/circle/detail?id=\" + data.postId,\r\n                title: '好有分享给您一条消息',\r\n                summary: data.content,\r\n                imageUrl: data.slt || data.postMediaList[0].mediaUrl || null,\r\n                success: function (res) {\r\n                    console.log(\"success:\" + JSON.stringify(res));\r\n                },\r\n                fail: function (err) {\r\n                    console.log(\"fail:\" + JSON.stringify(err));\r\n                }\r\n            });\r\n        },\r\n        thumbUpPosts(data, index){\r\n            post('/system/wx/posts/likes',data).then((res) => {\r\n                if (res.code == '200') {\r\n                    this.$set(this.postsList[index], 'isLikes', this.postsList[index].isLikes == 1? 0: 1)\r\n                    this.$set(this.postsList[index], 'likeCount', this.postsList[index].isLikes == 1?this.postsList[index].likeCount + 1: this.postsList[index].likeCount - 1)\r\n                }\r\n            })\r\n        },\r\n        handlerDelete(item){\r\n            let that = this\r\n            uni.showModal({\r\n                title: '删除确认',\r\n                content: '删除后不可恢复，是否删除？',\r\n                success: function (res) {\r\n                    if (res.confirm) {\r\n                        del('/system/wx/posts/'+ item.id).then(() => {\r\n                            uni.showToast({ title: '删除成功', icon: 'none' })\r\n                            that.params.pageNum = 1\r\n                            that.getPostsList()\r\n                        })\r\n                    } else if (res.cancel) {\r\n                        console.log('用户点击取消');\r\n                    }\r\n                }\r\n            });\r\n        }\r\n\r\n    },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n  width: 100%;\r\n  height: 100vh;\r\n  padding: 0rpx 25rpx;\r\n  overflow: hidden;\r\n  box-sizing: border-box;\r\n  background: linear-gradient(to bottom, #fcc7ba 0%, #ffffff 30%);\r\n\r\n  .navbarBox {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 30rpx;\r\n\r\n    .release,\r\n    .city {\r\n      display: flex;\r\n      gap: 10rpx;\r\n      padding: 10rpx 30rpx;\r\n      color: #020202;\r\n      background: rgba($color: #ffffff, $alpha: 0.6);\r\n      border: 1rpx solid #ff9012;\r\n      border-radius: 30rpx;\r\n    }\r\n  }\r\n\r\n  .scrollBox {\r\n    width: 100%;\r\n\r\n    .contentBox .item {\r\n      width: 100%;\r\n      padding: 50rpx 0rpx 30rpx;\r\n      border-bottom: 1rpx solid #ececec;\r\n      box-sizing: border-box;\r\n\r\n      .header {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        margin-bottom: 30rpx;\r\n        gap: 30rpx;\r\n\r\n        .left {\r\n            flex: 1;\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 10rpx;\r\n          overflow: hidden;\r\n        }\r\n        .left .avater {\r\n          width: 80rpx;\r\n          height: 80rpx;\r\n          border-radius: 50%;\r\n          flex-shrink: 0;\r\n        }\r\n        .left .info{\r\n            flex: 1;\r\n            overflow: hidden;\r\n        }\r\n        .left .info .name {\r\n            flex: 1;\r\n            font-size: 35rpx;\r\n            color: #020202;\r\n            line-height: 45rpx;\r\n            font-weight: 500;\r\n            white-space: nowrap;\r\n            text-overflow: ellipsis;\r\n            overflow: hidden;\r\n        }\r\n        .left .info .time {\r\n          font-size: 28rpx;\r\n          color: #999;\r\n          line-height: 41rpx;\r\n        }\r\n\r\n        .right{\r\n            flex-shrink: 0;\r\n        }\r\n      }\r\n\r\n      .content {\r\n        width: 100%;\r\n\r\n        .textBox {\r\n          font-size: 30rpx;\r\n          color: #020202;\r\n          line-height: 45rpx;\r\n          white-space: wrap;\r\n          margin-bottom: 20rpx;\r\n        }\r\n\r\n        .mideo {\r\n          width: 100%;\r\n          display: flex;\r\n          gap: 20rpx 20rpx;\r\n          flex-wrap: wrap;\r\n\r\n          image {\r\n            flex: 1;\r\n            min-width: 30%;\r\n            border-radius: 16rpx;\r\n          }\r\n        }\r\n      }\r\n      .bottomBtnBox{\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-top: 30rpx;\r\n        color: #999;\r\n      }\r\n      .bottomBtn {\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        gap: 50rpx;\r\n\r\n        .iconBox {\r\n          display: flex;\r\n          align-items: center;\r\n          font-size: 32rpx;\r\n          color: #999;\r\n          gap: 8rpx;\r\n\r\n          .shareBtn{\r\n            display: flex;\r\n            font-size: 32rpx;\r\n            color: #999;\r\n            background: transparent;\r\n\r\n            &::after{\r\n              display: none;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .releaseList {\r\n    padding: 50rpx;\r\n    display: flex;\r\n    gap: 30rpx;\r\n    border-radius: 20rpx;\r\n    overflow: hidden;\r\n    box-sizing: border-box;\r\n\r\n    .item {\r\n      width: 90rpx;\r\n      padding: 20rpx 30rpx;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      align-items: center;\r\n      gap: 10rpx;\r\n      font-size: 29rpx;\r\n      font-weight: bold;\r\n      color: #020202;\r\n      background: rgba($color: #c5c4c4, $alpha: 0.2);\r\n      border-radius: 10rpx;\r\n\r\n      image {\r\n        width: 50rpx;\r\n        height: 50rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=92537b68&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=92537b68&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756730882268\n      var cssReload = require(\"D:/soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}