<template>
  <view class="container">
    <view class="content">
      <view class="item">
        <view class="label">头像</view>
        <view class="value">
          <button :class="['avatar']" open-type="chooseAvatar" @chooseavatar="chooseavatar">
            <image :src="info.picUrl" />
          </button>
          <u-icon name="arrow-right" v-if="editStatus" color="#999" size="15"></u-icon>
        </view>
      </view>
      <view class="item">
        <view class="label">昵称</view>
        <view class="value">
          <input type="nickname" v-model="info.nickName" :disabled="!editStatus" />
        </view>
      </view>
      <view class="item">
        <view class="label">年龄</view>
        <view class="value">
          <input type="number" v-model="info.age" :disabled="!editStatus" />
        </view>
      </view>
      <view class="item">
        <view class="label">真实姓名</view>
        <view class="value">
          <input type="text" v-model="info.realName" :disabled="!editStatus" />
        </view>
      </view>
      <view class="item">
        <view class="label">性别</view>
        <view class="value" @click="slectSex">
          <view>{{ info.sex == 1? '男': '女'}}</view>
          <u-icon name="arrow-right" color="#999" size="15" v-if="editStatus"></u-icon>
        </view>
      </view>
    </view>

    <view class="btn" v-if="!editStatus" @click="editStatus = true">编辑</view>
    <view class="btn btn2" v-if="editStatus" @click="cancle">取消</view>
    <view class="btn btn3" v-if="editStatus" @click="saveInfo">保存</view>

    <u-action-sheet :actions="list" :show="sexStatus" cancelText="取消"  @select="selectClick" @close="sexStatus = false"></u-action-sheet>
  </view>
</template>

<script>
import { post } from '@/utils/request.js'
import { mapState } from 'vuex'
export default {
  data() {
    return {
        editStatus: false,
        sexStatus: false,
        list: [
            {
                name:'男',
                value: 1
            },{
                name:'女',
                value: 2
            }
        ],
        info: {}
    }
  },
    computed: {
        ...mapState(['userInfo'])
    },
    onLoad(options) {
        this.info = this.userInfo
    },
  methods: {
    chooseavatar(e){
      let self = this;
        let { avatarUrl } = e.detail;

        uni.showLoading({
            title: '加载中'
        });
        uni.uploadFile({
            url: 'https://xdmcyh.com/prod-api/common/uploadWx',
            filePath: avatarUrl,
            name: 'file',
            success: uploadFileRes => {
                // 注意：这里返回的uploadFileRes.data 为JSON 需要自己去转换
                let data = JSON.parse(uploadFileRes.data);
                if (data.code === 200) {
                    this.info.picUrl = data.msg;
                }
            },
            fail: (error) => {
                uni.showToast({
                    title: error,
                    duration: 2000
                });
            },
            complete: () => {
                uni.hideLoading();
            }
        });
    },
    cancle(){
      this.editStatus = false
    },
    slectSex(){
      if(this.editStatus){
        this.sexStatus = true
      }
    },
    selectClick(e){
        this.info.sex = e.value
    },
    saveInfo(){
        if(!this.userInfo.nickName.trim()){
            return uni.showToast({
                title: '昵称不能为空',
                icon: 'none'
            })
        }
        post('/system/wx/updateWxUser',this.info).then(res => {
            console.log(res)
            uni.showToast({
                title: '保存成功',
                icon: 'none'
            })
            this.editStatus = false
            uni.navigateBack({ delta: 1 })
        }).catch(err => {
            console.log(err)
        })
    }
  },
}
</script>

<style lang="scss" scoped>
  .container{
    width: 100%;
    height: 100vh;
    padding: 0 30rpx;
    background: #f5f5f5;
    overflow: hidden;
    box-sizing: border-box;

    .content .item{
      width: 100%;
      height: 100rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1rpx solid #e0e0e0;
      color: #666;

      .value{
        display: flex;
        align-items: center;

        input{
          text-align: right;
        }
        .avatar{
          width: 50rpx;
          height: 50rpx;
          border-radius: 50%;
          padding: 0rpx;
          line-height: 0rpx;
        }
        .avatar image{
          width: 50rpx;
          height: 50rpx;
          border-radius: 50%;
        }
      }
    }

    .btn{
      width: 100%;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 20rpx;
      color: #fff;
      background: #71b168;
      margin-top: 50rpx;
    }
    .btn2{
      background: #cccac7;
    }
    .btn3{
      background: #ff9900;
    }
  }
</style>