<template>
  <view class="container">
    <view class="content">
      <view class="item">
        <view class="label">头像</view>
        <view class="value">
          <button class="avatar" open-type="chooseAvatar" @chooseavatar="chooseavatar">
            <image
              src="https://cbu01.alicdn.com/img/ibank/O1CN01xHYrOo1Bs2ywlIm2k_!!0-0-cib.jpg"
            />
          </button>
          <u-icon name="arrow-right" color="#999" size="15"></u-icon>
        </view>
      </view>
      <view class="item">
        <view class="label">昵称</view>
        <view class="value">
          <input type="text" value="********" :disabled="!editStatus" />
        </view>
      </view>
      <view class="item">
        <view class="label">性别</view>
        <view class="value" @click="slectSex">
          <view>男</view>
          <u-icon name="arrow-right" color="#999" size="15"></u-icon>
        </view>
      </view>
    </view>

    <view class="btn" v-if="!editStatus" @click="editStatus = true">编辑</view>
    <view class="btn btn2" v-if="editStatus" @click="cancle">取消</view>
    <view class="btn btn3" v-if="editStatus">保存</view>

    <u-action-sheet :actions="list" :show="sexStatus" cancelText="取消"  @select="selectClick" @close="sexStatus = false"></u-action-sheet>
  </view>
</template>

<script>
export default {
  data() {
    return {
      editStatus: false,
      sexStatus: false,
      list: [
				{
					name:'男'
				},
        {
					name:'女'
				}
			],
    }
  },
  methods: {
    chooseavatar(e){
      console.log(111)
    },
    cancle(){
      this.editStatus = false
    },
    slectSex(){
      if(this.editStatus){
        this.sexStatus = true
      }
    },
    selectClick(){}
  },
}
</script>

<style lang="scss" scoped>
  .container{
    width: 100%;
    height: 100vh;
    padding: 0 30rpx;
    background: #f5f5f5;
    overflow: hidden;
    box-sizing: border-box;

    .content .item{
      width: 100%;
      height: 100rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1rpx solid #e0e0e0;
      color: #666;

      .value{
        display: flex;
        align-items: center;

        input{
          text-align: right;
        }
        .avatar{
          width: 50rpx;
          height: 50rpx;
          border-radius: 50%;
          padding: 0rpx;
          line-height: 0rpx;
        }
        .avatar image{
          width: 50rpx;
          height: 50rpx;
          border-radius: 50%;
        }
      }
    }

    .btn{
      width: 100%;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 20rpx;
      color: #fff;
      background: #71b168;
      margin-top: 50rpx;
    }
    .btn2{
      background: #cccac7;
    }
    .btn3{
      background: #ff9900;
    }
  }
</style>