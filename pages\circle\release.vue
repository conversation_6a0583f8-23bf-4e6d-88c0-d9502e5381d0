<template>
  <view class="container">
    <view class="textBox">
      <u-textarea v-model="content" height="150" placeholder="请输入内容" border="bottom"></u-textarea>
    </view>

    <view class="medioBox" v-show="type != 0">
      <u-upload
        :accept="type == 1? 'image ': 'video'"
        :fileList="fileList"
        @afterRead="afterRead"
        @delete="deletePic"
        :multiple="type == 1? true: false"
        :maxCount="type == 1? 9: 1"
        :previewFullImage="true"
      ></u-upload>
    </view>

    <view class="btnBox" @click="handlerRelease">发布</view>
  </view>
</template>

<script>
import { get, post } from '@/utils/request.js'
import { mapState } from 'vuex'

export default {
  data() {
    return {
      content: '',
      type: 0,
      fileList: []
    }
  },
  computed: {
    ...mapState(['city', 'userInfo'])
  },
  onLoad(options) {
    this.type = options.type || 0
  },
  methods: {
    deletePic(event) {
      this.fileList.splice(event.index, 1);
    },
    async afterRead(event) {
      let lists = [].concat(event.file);
      let fileListLen = this.fileList.length;
      lists.map((item) => {
        this.fileList.push({
          ...item,
          status: "uploading",
          message: "上传中",
        });
      });
      for (let i = 0; i < lists.length; i++) {
        const result = await this.uploadFilePromise(lists[i].url);
        let item = this.fileList[fileListLen];
        this.fileList.splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: "success",
            message: "",
            url: result,
          })
        );
        fileListLen++;
      }
    },

    uploadFilePromise(url) {
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: "https://xdmcyh.com/prod-api/common/uploadWx", // 仅为示例，非真实的接口地址
          filePath: url,
          name: "file",
          formData: {
            user: "test",
          },
          success: (res) => {
            if(res.data){
                let result = JSON.parse(res.data)
                resolve(result.msg);
            }
          },fail:(fail)=>{
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            })
          },
        });
      });
    },
    
    handlerRelease(){
        if(!this.content.trim()) {
            return uni.showToast({ title: '请输入内容', icon: 'none' })
        }
        if(this.type != 0 && !this.fileList.length) {
            return uni.showToast({ title: '请上传图片或视频', icon: 'none' })
        }
        let params = {
            city: this.city,
            content: this.content,
            mediaType: this.type
        }

        if(this.type != 0){
            let list = this.fileList.map((el) => el.url)
            params.mediaList = list
        }

        post('/system/wx/posts/add', params).then(res => {
            if(res.code == 200){
                uni.showToast({ title: '发布成功', icon: 'none' })
                setTimeout(() => {
                    uni.navigateBack({ delta: 1 })
                }, 1500);
            }
        }).catch(err => {
            console.log(err)
        })
    }
  },

}
</script>


<style lang="scss" scoped>
  .container{
    width: 100%;
    padding: 30rpx;
    box-sizing: border-box;
  }

  .medioBox{
    margin-top: 30rpx;
  }
  .btnBox{
    margin-top: 50rpx;
    width: 100%;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 34rpx;
    color: #fff;
    background: #f39509;
    border-radius: 15rpx;
  }
</style>