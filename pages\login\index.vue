<template>
  <view class="login-container">
    <!-- 登录按钮 -->
    <view class="login-button-container">
      <button class="login-button" type="primary" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
        <text class="button-text">手机号快捷登录</text>
      </button>
    </view>
  </view>
</template>

<script>
import { get } from '@/utils/request.js'
export default {
  data() {
    return {
      code: ''
    }
  },
  onLoad(options) {
  },
  methods: {
    getPhoneNumber(e) {
        let that = this
        uni.login({
            provider: 'weixin', //使用微信登录
            success: function (loginRes) {
                let data = {
                    encryptedData: e.detail.encryptedData,
                    iv: e.detail.iv,
                    code: loginRes.code
                }
                get('/system/wx/user/initLogin', data).then(res => {
                    if(res.code == '200'){
                        that.$store.commit('setToken', res.data.token)
                        that.$store.commit('setUserInfo', res.data)
                        uni.reLaunch({
                            url:'/pages/index/index',
                        })
                    }
                }).catch(err => {
                    console.log(err)
                }).finally(() => {
                    that.loginStatus = false
                })
            }
        })
    },
  },

}
</script>


<style scoped lang="scss">
  .login-container{
    width: 100%;
    height: 100vh;

    .login-button-container{
      width: 500rpx;
      margin: 100rpx auto;
    }
  }
</style>