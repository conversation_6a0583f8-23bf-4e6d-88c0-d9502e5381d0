{"version": 3, "sources": ["webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u--form/u--form.vue?ab92", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u--form/u--form.vue?5196", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u--form/u--form.vue?5cd2", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u--form/u--form.vue?caad", "uni-app:///uni_modules/uview-ui/components/u--form/u--form.vue"], "names": ["name", "mixins", "components", "uvForm", "created", "methods", "setRules", "validate", "validateField", "resetFields", "clearValidate", "setMpData"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;;;AAGtD;AAC2M;AAC3M,gBAAgB,4MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4yB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACuBh0B;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EAEAA;EAKAC;EACAC;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACAC;MACA;AACA;AACA;AACA;AACA;;MAEA;MAEA;IACA;IACAC;MAEA;MAEA;IACA;IACAC;MAEA;MAEA;IACA;IACAC;MAEA;MAEA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B", "file": "uni_modules/uview-ui/components/u--form/u--form.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u--form.vue?vue&type=template&id=5e58019d&\"\nvar renderjs\nimport script from \"./u--form.vue?vue&type=script&lang=js&\"\nexport * from \"./u--form.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u--form/u--form.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u--form.vue?vue&type=template&id=5e58019d&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u--form.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u--form.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<uvForm\r\n\t\tref=\"uForm\"\r\n\t\t:model=\"model\"\r\n\t\t:rules=\"rules\"\r\n\t\t:errorType=\"errorType\"\r\n\t\t:borderBottom=\"borderBottom\"\r\n\t\t:labelPosition=\"labelPosition\"\r\n\t\t:labelWidth=\"labelWidth\"\r\n\t\t:labelAlign=\"labelAlign\"\r\n\t\t:labelStyle=\"labelStyle\"\r\n\t\t:customStyle=\"customStyle\"\r\n\t>\r\n\t\t<slot />\r\n\t</uvForm>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * 此组件存在的理由是，在nvue下，u-form被uni-app官方占用了，u-form在nvue中相当于form组件\r\n\t * 所以在nvue下，取名为u--form，内部其实还是u-form.vue，只不过做一层中转\r\n\t */\r\n\timport uvForm from '../u-form/u-form.vue';\r\n\timport props from '../u-form/props.js'\n\texport default {\r\n\t\t// #ifdef MP-WEIXIN\r\n\t\tname: 'u-form',\r\n\t\t// #endif\r\n\t\t// #ifndef MP-WEIXIN\r\n\t\tname: 'u--form',\r\n\t\t// #endif\r\n\t\tmixins: [uni.$u.mpMixin, props, uni.$u.mixin],\r\n\t\tcomponents: {\r\n\t\t\tuvForm\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.children = []\r\n\t\t},\r\n\t\tmethods: {\n\t\t\t// 手动设置校验的规则，如果规则中有函数的话，微信小程序中会过滤掉，所以只能手动调用设置规则\n\t\t\tsetRules(rules) {\n\t\t\t\tthis.$refs.uForm.setRules(rules)\n\t\t\t},\r\n\t\t\tvalidate() {\r\n\t\t\t\t/**\r\n\t\t\t\t * 在微信小程序中，通过this.$parent拿到的父组件是u--form，而不是其内嵌的u-form\r\n\t\t\t\t * 导致在u-form组件中，拿不到对应的children数组，从而校验无效，所以这里每次调用u-form组件中的\r\n\t\t\t\t * 对应方法的时候，在小程序中都先将u--form的children赋值给u-form中的children\r\n\t\t\t\t */\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tthis.setMpData()\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn this.$refs.uForm.validate()\r\n\t\t\t},\r\n\t\t\tvalidateField(value, callback, event) {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tthis.setMpData()\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn this.$refs.uForm.validateField(value, callback, event)\r\n\t\t\t},\r\n\t\t\tresetFields() {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tthis.setMpData()\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn this.$refs.uForm.resetFields()\r\n\t\t\t},\r\n\t\t\tclearValidate(props) {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tthis.setMpData()\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn this.$refs.uForm.clearValidate(props)\r\n\t\t\t},\r\n\t\t\tsetMpData() {\r\n\t\t\t\tthis.$refs.uForm.children = this.children\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n"], "sourceRoot": ""}