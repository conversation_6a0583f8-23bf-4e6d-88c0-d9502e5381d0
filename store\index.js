// 页面路径：store/index.js 
import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex);//vue的插件机制

//Vuex.Store 构造器选项
const store = new Vuex.Store({
	state:{//存放状态
        city: uni.getStorageSync('CITY') || '',
        userInfo: uni.getStorageSync('USERINFO') || {},
        token: uni.getStorageSync('TOKEN') || '',

        cityList: uni.getStorageSync('CITYLIST') || []
	},
  mutations:{//修改状态
        setCity(state, data){
            state.city = data
            uni.setStorageSync('CITY', data)
        },
        setUserInfo(state, userData){
            state.userInfo = userData
            uni.setStorageSync('USERINFO', userData)
        },
        setToken(state, data){
            state.token = data
            uni.setStorageSync('TOKEN', data)
        },
        setCityList(state, data){
            state.cityList = data
            uni.setStorageSync('CITYLIST', data)
        }
	}
})
export default store
