{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/code/work/rescue/pages/user/editInfo.vue?4fa4", "webpack:///D:/code/work/rescue/pages/user/editInfo.vue?5e22", "webpack:///D:/code/work/rescue/pages/user/editInfo.vue?6152", "webpack:///D:/code/work/rescue/pages/user/editInfo.vue?c8f3", "uni-app:///pages/user/editInfo.vue", "webpack:///D:/code/work/rescue/pages/user/editInfo.vue?4529", "webpack:///D:/code/work/rescue/pages/user/editInfo.vue?e50a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "editStatus", "sexStatus", "list", "name", "value", "info", "computed", "onLoad", "methods", "<PERSON><PERSON><PERSON>", "uni", "title", "url", "filePath", "success", "fail", "duration", "complete", "cancle", "slectSex", "selectClick", "saveInfo", "icon", "console", "delta"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACqM;AACrM,gBAAgB,4MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,iVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAA+wB,CAAgB,6wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACgDnyB;AACA;AAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC,OACA;QACAC;QACAC;MACA;QACAD;QACAC;MACA,EACA;MACAC;IACA;EACA;EACAC,4BACA,kCACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MAEAC;QACAC;MACA;MACAD;QACAE;QACAC;QACAV;QACAW;UACA;UACA;UACA;YACA;UACA;QACA;QACAC;UACAL;YACAC;YACAK;UACA;QACA;QACAC;UACAP;QACA;MACA;IACA;IACAQ;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;UACAV;UACAW;QACA;MACA;MACA;QACAC;QACAb;UACAC;UACAW;QACA;QACA;QACAZ;UAAAc;QAAA;MACA;QACAD;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtIA;AAAA;AAAA;AAAA;AAA09C,CAAgB,g5CAAG,EAAC,C;;;;;;;;;;;ACA9+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/editInfo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/editInfo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./editInfo.vue?vue&type=template&id=e17fac1a&scoped=true&\"\nvar renderjs\nimport script from \"./editInfo.vue?vue&type=script&lang=js&\"\nexport * from \"./editInfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./editInfo.vue?vue&type=style&index=0&id=e17fac1a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e17fac1a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/editInfo.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editInfo.vue?vue&type=template&id=e17fac1a&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uActionSheet: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-action-sheet/u-action-sheet\" */ \"@/uni_modules/uview-ui/components/u-action-sheet/u-action-sheet.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.editStatus = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.sexStatus = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editInfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editInfo.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <view class=\"content\">\r\n      <view class=\"item\">\r\n        <view class=\"label\">头像</view>\r\n        <view class=\"value\">\r\n          <button :class=\"['avatar']\" open-type=\"chooseAvatar\" @chooseavatar=\"chooseavatar\">\r\n            <image :src=\"info.picUrl\" />\r\n          </button>\r\n          <u-icon name=\"arrow-right\" v-if=\"editStatus\" color=\"#999\" size=\"15\"></u-icon>\r\n        </view>\r\n      </view>\r\n      <view class=\"item\">\r\n        <view class=\"label\">昵称</view>\r\n        <view class=\"value\">\r\n          <input type=\"nickname\" v-model=\"info.nickName\" :disabled=\"!editStatus\" />\r\n        </view>\r\n      </view>\r\n      <view class=\"item\">\r\n        <view class=\"label\">年龄</view>\r\n        <view class=\"value\">\r\n          <input type=\"number\" v-model=\"info.age\" :disabled=\"!editStatus\" />\r\n        </view>\r\n      </view>\r\n      <view class=\"item\">\r\n        <view class=\"label\">真实姓名</view>\r\n        <view class=\"value\">\r\n          <input type=\"text\" v-model=\"info.realName\" :disabled=\"!editStatus\" />\r\n        </view>\r\n      </view>\r\n      <view class=\"item\">\r\n        <view class=\"label\">性别</view>\r\n        <view class=\"value\" @click=\"slectSex\">\r\n          <view>{{ info.sex == 1? '男': '女'}}</view>\r\n          <u-icon name=\"arrow-right\" color=\"#999\" size=\"15\" v-if=\"editStatus\"></u-icon>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"btn\" v-if=\"!editStatus\" @click=\"editStatus = true\">编辑</view>\r\n    <view class=\"btn btn2\" v-if=\"editStatus\" @click=\"cancle\">取消</view>\r\n    <view class=\"btn btn3\" v-if=\"editStatus\" @click=\"saveInfo\">保存</view>\r\n\r\n    <u-action-sheet :actions=\"list\" :show=\"sexStatus\" cancelText=\"取消\"  @select=\"selectClick\" @close=\"sexStatus = false\"></u-action-sheet>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { post } from '@/utils/request.js'\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n  data() {\r\n    return {\r\n        editStatus: false,\r\n        sexStatus: false,\r\n        list: [\r\n            {\r\n                name:'男',\r\n                value: 1\r\n            },{\r\n                name:'女',\r\n                value: 2\r\n            }\r\n        ],\r\n        info: {}\r\n    }\r\n  },\r\n    computed: {\r\n        ...mapState(['userInfo'])\r\n    },\r\n    onLoad(options) {\r\n        this.info = this.userInfo\r\n    },\r\n  methods: {\r\n    chooseavatar(e){\r\n      let self = this;\r\n        let { avatarUrl } = e.detail;\r\n\r\n        uni.showLoading({\r\n            title: '加载中'\r\n        });\r\n        uni.uploadFile({\r\n            url: 'https://xdmcyh.com/prod-api/common/uploadWx',\r\n            filePath: avatarUrl,\r\n            name: 'file',\r\n            success: uploadFileRes => {\r\n                // 注意：这里返回的uploadFileRes.data 为JSON 需要自己去转换\r\n                let data = JSON.parse(uploadFileRes.data);\r\n                if (data.code === 200) {\r\n                    this.info.picUrl = data.msg;\r\n                }\r\n            },\r\n            fail: (error) => {\r\n                uni.showToast({\r\n                    title: error,\r\n                    duration: 2000\r\n                });\r\n            },\r\n            complete: () => {\r\n                uni.hideLoading();\r\n            }\r\n        });\r\n    },\r\n    cancle(){\r\n      this.editStatus = false\r\n    },\r\n    slectSex(){\r\n      if(this.editStatus){\r\n        this.sexStatus = true\r\n      }\r\n    },\r\n    selectClick(e){\r\n        this.info.sex = e.value\r\n    },\r\n    saveInfo(){\r\n        if(!this.userInfo.nickName.trim()){\r\n            return uni.showToast({\r\n                title: '昵称不能为空',\r\n                icon: 'none'\r\n            })\r\n        }\r\n        post('/system/wx/updateWxUser',this.info).then(res => {\r\n            console.log(res)\r\n            uni.showToast({\r\n                title: '保存成功',\r\n                icon: 'none'\r\n            })\r\n            this.editStatus = false\r\n            uni.navigateBack({ delta: 1 })\r\n        }).catch(err => {\r\n            console.log(err)\r\n        })\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .container{\r\n    width: 100%;\r\n    height: 100vh;\r\n    padding: 0 30rpx;\r\n    background: #f5f5f5;\r\n    overflow: hidden;\r\n    box-sizing: border-box;\r\n\r\n    .content .item{\r\n      width: 100%;\r\n      height: 100rpx;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      border-bottom: 1rpx solid #e0e0e0;\r\n      color: #666;\r\n\r\n      .value{\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        input{\r\n          text-align: right;\r\n        }\r\n        .avatar{\r\n          width: 50rpx;\r\n          height: 50rpx;\r\n          border-radius: 50%;\r\n          padding: 0rpx;\r\n          line-height: 0rpx;\r\n        }\r\n        .avatar image{\r\n          width: 50rpx;\r\n          height: 50rpx;\r\n          border-radius: 50%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .btn{\r\n      width: 100%;\r\n      height: 80rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border-radius: 20rpx;\r\n      color: #fff;\r\n      background: #71b168;\r\n      margin-top: 50rpx;\r\n    }\r\n    .btn2{\r\n      background: #cccac7;\r\n    }\r\n    .btn3{\r\n      background: #ff9900;\r\n    }\r\n  }\r\n</style>", "import mod from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editInfo.vue?vue&type=style&index=0&id=e17fac1a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./editInfo.vue?vue&type=style&index=0&id=e17fac1a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756730694082\n      var cssReload = require(\"D:/soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}