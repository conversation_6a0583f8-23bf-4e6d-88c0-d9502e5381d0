<template>
  <view class="container">
    <view class="headerBox" :style="{marginTop: (statusBarHeight + navBarHeight) + 'px'}">
      <!-- <view>我的</view> -->
      <!-- <u-icon name="setting-fill" color="#666" size="26"></u-icon> -->
    </view>

    <view class="userInfo" @click="editInfo">
      <view class="avater">
        <image
          src="https://cbu01.alicdn.com/img/ibank/O1CN01xHYrOo1Bs2ywlIm2k_!!0-0-cib.jpg"
        />
      </view>
      <view class="name">************</view>
    </view>

    <view class="infoDetail">
      <view class="item">
        <view class="num">0</view>
        <view class="text">优惠券</view>
      </view>
      <view class="item">
        <view class="num">0</view>
        <view class="text">粉丝</view>
      </view>
      <view class="item">
        <view class="num">0</view>
        <view class="text">积分</view>
      </view>
      <view class="item">
        <view class="num">0</view>
        <view class="text">发布</view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  data(){
    return {
      statusBarHeight: 0,
      navBarHeight: 0,
    }
  },
    computed: {
        ...mapState(['token', 'userInfo'])
    },
  onLoad(options) {
    if(!token){
        uni.navigateTo({
            url:'/pages/login/index.vue',
        })
        return 
    }
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;

    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    if (menuButtonInfo) {
      this.navBarHeight = (menuButtonInfo.top - this.statusBarHeight) * 2 + menuButtonInfo.height;
    }
  },
  methods: {
    editInfo(){
      uni.navigateTo({
        url:'/pages/user/editInfo',
      })
    }
  },
}
</script>

<style lang="scss" scoped>
  .container{
    width: 100%;
    height: 100vh;
    padding: 0rpx 25rpx;
    box-sizing: border-box;
    background: linear-gradient(to bottom, #fcc7ba 0%, #ebebeb 30%);
    overflow: hidden;

    .headerBox{
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: bold;
      box-sizing: border-box;
      margin-bottom: 30rpx;
    }

    .userInfo{
      display: flex;
      align-items: center;
      gap: 20rpx;
      margin-bottom: 30rpx;

      .avater, .avater image{
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
      }
      .name{
        font-size: 35rpx;
        font-weight: bold;
      }
    }

    .infoDetail{
      width: 100%;
      padding: 30rpx;
      display: flex;
      justify-content: space-around;
      box-sizing: border-box;
      background: #fff;
      border-radius: 20rpx;

      .item{
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 15rpx;
      }
    }
  }
</style>