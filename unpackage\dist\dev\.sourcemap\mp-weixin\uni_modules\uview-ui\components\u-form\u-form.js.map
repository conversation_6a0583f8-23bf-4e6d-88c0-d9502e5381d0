{"version": 3, "sources": ["webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-form/u-form.vue?7f43", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-form/u-form.vue?36ff", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-form/u-form.vue?e855", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-form/u-form.vue?0ce3", "uni-app:///uni_modules/uview-ui/components/u-form/u-form.vue"], "names": ["<PERSON><PERSON><PERSON>", "name", "mixins", "provide", "uForm", "data", "formRules", "validator", "originalModel", "watch", "rules", "immediate", "handler", "props<PERSON><PERSON>e", "child", "model", "computed", "created", "methods", "setRules", "uni", "resetFields", "resetModel", "clearValidate", "props", "validateField", "event", "value", "property<PERSON>hain", "propertyName", "errorsRes", "childErrors", "validate", "reject", "resolve"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;;;AAGrD;AAC2M;AAC3M,gBAAgB,4MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2yB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACO/zB;AACA;;;;;;;;AACA;AACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA,eAcA;EACAC;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA,+CACAC;QACA;MACA;IACA;IACA;IACAC;MACAJ;MACAC;QACA;UACA;QACA;MACA;IACA;EACA;EACAI;IACAH;MACA,QACA,gBACA,mBACA,oBACA,iBACA,iBACA,gBACA;IACA;EACA;EACAI;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;QACAC;QACA;MACA;MAAA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACAF;MACA;IACA;IACA;IACAG;MACAC;MACA;QACA;QACA;UACAV;QACA;MACA;IACA;IACA;IACAW;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBACA;gBACA;kBACA;kBACA;kBACA;kBACAC;kBACA;kBACA;oBACA;oBACA;oBACA;sBACA;sBACA,qCACA,cACAb,WACA;sBACA;sBACA;sBACA,mBACAc;sBAEA;sBACA;sBACA;sBACA;sBACA;;sBAEA;sBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA,8EACAC,wBACA;wBACAtB,qDACAsB,4BAEA;0BAAA;0BACA;4BACAC;4BACAC;0BACA;0BACAjB,0DACAiB;wBACA,EACA;sBACA;oBACA;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACAZ;QACA;MACA;MACA;QACA;QACA;UACA;UACA,wCACA;YAAA;UAAA,EACA;UACA;YACA;cACA;cACA;cACAa;YACA;cACAC;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "uni_modules/uview-ui/components/u-form/u-form.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-form.vue?vue&type=template&id=786a592e&scoped=true&\"\nvar renderjs\nimport script from \"./u-form.vue?vue&type=script&lang=js&\"\nexport * from \"./u-form.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"786a592e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-form/u-form.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-form.vue?vue&type=template&id=786a592e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-form.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-form.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-form\">\r\n\t\t<slot />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from \"./props.js\";\r\n\timport Schema from \"../../libs/util/async-validator\";\r\n\t// 去除警告信息\r\n\tSchema.warning = function() {};\r\n\t/**\r\n\t * Form 表单\r\n\t * @description 此组件一般用于表单场景，可以配置Input输入框，Select弹出框，进行表单验证等。\r\n\t * @tutorial https://www.uviewui.com/components/form.html\r\n\t * @property {Object}\t\t\t\t\t\tmodel\t\t\t当前form的需要验证字段的集合\r\n\t * @property {Object | Function | Array}\trules\t\t\t验证规则\r\n\t * @property {String}\t\t\t\t\t\terrorType\t\t错误的提示方式，见上方说明 ( 默认 message )\r\n\t * @property {Boolean}\t\t\t\t\t\tborderBottom\t是否显示表单域的下划线边框   ( 默认 true ）\r\n\t * @property {String}\t\t\t\t\t\tlabelPosition\t表单域提示文字的位置，left-左侧，top-上方 ( 默认 'left' ）\r\n\t * @property {String | Number}\t\t\t\tlabelWidth\t\t提示文字的宽度，单位px  ( 默认 45 ）\r\n\t * @property {String}\t\t\t\t\t\tlabelAlign\t\tlable字体的对齐方式   ( 默认 ‘left' ）\r\n\t * @property {Object}\t\t\t\t\t\tlabelStyle\t\tlable的样式，对象形式\r\n\t * @example <u--formlabelPosition=\"left\" :model=\"model1\" :rules=\"rules\" ref=\"form1\"></u--form>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-form\",\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n\t\tprovide() {\r\n\t\t\treturn {\r\n\t\t\t\tuForm: this,\r\n\t\t\t};\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tformRules: {},\r\n\t\t\t\t// 规则校验器\r\n\t\t\t\tvalidator: {},\r\n\t\t\t\t// 原始的model快照，用于resetFields方法重置表单时使用\r\n\t\t\t\toriginalModel: null,\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 监听规则的变化\r\n\t\t\trules: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(n) {\r\n\t\t\t\t\tthis.setRules(n);\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t\t// 监听属性的变化，通知子组件u-form-item重新获取信息\r\n\t\t\tpropsChange(n) {\r\n\t\t\t\tif (this.children?.length) {\r\n\t\t\t\t\tthis.children.map((child) => {\r\n\t\t\t\t\t\t// 判断子组件(u-form-item)如果有updateParentData方法的话，就就执行(执行的结果是子组件重新从父组件拉取了最新的值)\r\n\t\t\t\t\t\ttypeof child.updateParentData == \"function\" &&\r\n\t\t\t\t\t\t\tchild.updateParentData();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 监听model的初始值作为重置表单的快照\r\n\t\t\tmodel: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(n) {\r\n\t\t\t\t\tif (!this.originalModel) {\r\n\t\t\t\t\t\tthis.originalModel = uni.$u.deepClone(n);\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tpropsChange() {\r\n\t\t\t\treturn [\r\n\t\t\t\t\tthis.errorType,\r\n\t\t\t\t\tthis.borderBottom,\r\n\t\t\t\t\tthis.labelPosition,\r\n\t\t\t\t\tthis.labelWidth,\r\n\t\t\t\t\tthis.labelAlign,\r\n\t\t\t\t\tthis.labelStyle,\r\n\t\t\t\t];\r\n\t\t\t},\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// 存储当前form下的所有u-form-item的实例\r\n\t\t\t// 不能定义在data中，否则微信小程序会造成循环引用而报错\r\n\t\t\tthis.children = [];\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 手动设置校验的规则，如果规则中有函数的话，微信小程序中会过滤掉，所以只能手动调用设置规则\r\n\t\t\tsetRules(rules) {\r\n\t\t\t\t// 判断是否有规则\r\n\t\t\t\tif (Object.keys(rules).length === 0) return;\n\t\t\t\tif (process.env.NODE_ENV === 'development' && Object.keys(this.model).length === 0) {\n\t\t\t\t\tuni.$u.error('设置rules，model必须设置！如果已经设置，请刷新页面。');\n\t\t\t\t\treturn;\n\t\t\t\t};\r\n\t\t\t\tthis.formRules = rules;\r\n\t\t\t\t// 重新将规则赋予Validator\r\n\t\t\t\tthis.validator = new Schema(rules);\r\n\t\t\t},\r\n\t\t\t// 清空所有u-form-item组件的内容，本质上是调用了u-form-item组件中的resetField()方法\r\n\t\t\tresetFields() {\r\n\t\t\t\tthis.resetModel();\r\n\t\t\t},\r\n\t\t\t// 重置model为初始值的快照\r\n\t\t\tresetModel(obj) {\r\n\t\t\t\t// 历遍所有u-form-item，根据其prop属性，还原model的原始快照\r\n\t\t\t\tthis.children.map((child) => {\r\n\t\t\t\t\tconst prop = child?.prop;\r\n\t\t\t\t\tconst value = uni.$u.getProperty(this.originalModel, prop);\r\n\t\t\t\t\tuni.$u.setProperty(this.model, prop, value);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 清空校验结果\r\n\t\t\tclearValidate(props) {\r\n\t\t\t\tprops = [].concat(props);\r\n\t\t\t\tthis.children.map((child) => {\r\n\t\t\t\t\t// 如果u-form-item的prop在props数组中，则清除对应的校验结果信息\r\n\t\t\t\t\tif (props[0] === undefined || props.includes(child.prop)) {\r\n\t\t\t\t\t\tchild.message = null;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 对部分表单字段进行校验\r\n\t\t\tasync validateField(value, callback, event = null) {\r\n\t\t\t\t// $nextTick是必须的，否则model的变更，可能会延后于此方法的执行\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t// 校验错误信息，返回给回调方法，用于存放所有form-item的错误信息\r\n\t\t\t\t\tconst errorsRes = [];\r\n\t\t\t\t\t// 如果为字符串，转为数组\r\n\t\t\t\t\tvalue = [].concat(value);\r\n\t\t\t\t\t// 历遍children所有子form-item\r\n\t\t\t\t\tthis.children.map((child) => {\r\n\t\t\t\t\t\t// 用于存放form-item的错误信息\r\n\t\t\t\t\t\tconst childErrors = [];\r\n\t\t\t\t\t\tif (value.includes(child.prop)) {\r\n\t\t\t\t\t\t\t// 获取对应的属性，通过类似'a.b.c'的形式\r\n\t\t\t\t\t\t\tconst propertyVal = uni.$u.getProperty(\r\n\t\t\t\t\t\t\t\tthis.model,\r\n\t\t\t\t\t\t\t\tchild.prop\r\n\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t// 属性链数组\r\n\t\t\t\t\t\t\tconst propertyChain = child.prop.split(\".\");\r\n\t\t\t\t\t\t\tconst propertyName =\r\n\t\t\t\t\t\t\t\tpropertyChain[propertyChain.length - 1];\r\n\r\n\t\t\t\t\t\t\tconst rule = this.formRules[child.prop];\r\n\t\t\t\t\t\t\t// 如果不存在对应的规则，直接返回，否则校验器会报错\r\n\t\t\t\t\t\t\tif (!rule) return;\r\n\t\t\t\t\t\t\t// rule规则可为数组形式，也可为对象形式，此处拼接成为数组\r\n\t\t\t\t\t\t\tconst rules = [].concat(rule);\r\n\r\n\t\t\t\t\t\t\t// 对rules数组进行校验\r\n\t\t\t\t\t\t\tfor (let i = 0; i < rules.length; i++) {\r\n\t\t\t\t\t\t\t\tconst ruleItem = rules[i];\r\n\t\t\t\t\t\t\t\t// 将u-form-item的触发器转为数组形式\r\n\t\t\t\t\t\t\t\tconst trigger = [].concat(ruleItem?.trigger);\r\n\t\t\t\t\t\t\t\t// 如果是有传入触发事件，但是此form-item却没有配置此触发器的话，不执行校验操作\r\n\t\t\t\t\t\t\t\tif (event && !trigger.includes(event)) continue;\r\n\t\t\t\t\t\t\t\t// 实例化校验对象，传入构造规则\r\n\t\t\t\t\t\t\t\tconst validator = new Schema({\r\n\t\t\t\t\t\t\t\t\t[propertyName]: ruleItem,\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tvalidator.validate({\r\n\t\t\t\t\t\t\t\t\t\t[propertyName]: propertyVal,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t(errors, fields) => {\r\n\t\t\t\t\t\t\t\t\t\tif (uni.$u.test.array(errors)) {\r\n\t\t\t\t\t\t\t\t\t\t\terrorsRes.push(...errors);\r\n\t\t\t\t\t\t\t\t\t\t\tchildErrors.push(...errors);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tchild.message =\r\n\t\t\t\t\t\t\t\t\t\t\tchildErrors[0]?.message ?? null;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 执行回调函数\r\n\t\t\t\t\ttypeof callback === \"function\" && callback(errorsRes);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 校验全部数据\r\n\t\t\tvalidate(callback) {\n\t\t\t\t// 开发环境才提示，生产环境不会提示\n\t\t\t\tif (process.env.NODE_ENV === 'development' && Object.keys(this.formRules).length === 0) {\n\t\t\t\t\tuni.$u.error('未设置rules，请看文档说明！如果已经设置，请刷新页面。');\n\t\t\t\t\treturn;\n\t\t\t\t}\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\t// $nextTick是必须的，否则model的变更，可能会延后于validate方法\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t// 获取所有form-item的prop，交给validateField方法进行校验\r\n\t\t\t\t\t\tconst formItemProps = this.children.map(\r\n\t\t\t\t\t\t\t(item) => item.prop\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t\tthis.validateField(formItemProps, (errors) => {\r\n\t\t\t\t\t\t\tif(errors.length) {\r\n\t\t\t\t\t\t\t\t// 如果错误提示方式为toast，则进行提示\r\n\t\t\t\t\t\t\t\tthis.errorType === 'toast' && uni.$u.toast(errors[0].message)\r\n\t\t\t\t\t\t\t\treject(errors)\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tresolve(true)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n"], "sourceRoot": ""}