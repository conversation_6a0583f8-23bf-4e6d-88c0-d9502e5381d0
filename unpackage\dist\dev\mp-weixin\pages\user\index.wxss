@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-137d5072 {
  width: 100%;
  height: 100vh;
  padding: 0rpx 25rpx;
  box-sizing: border-box;
  background: linear-gradient(to bottom, #fcc7ba 0%, #ebebeb 30%);
  overflow: hidden;
}
.container .headerBox.data-v-137d5072 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: bold;
  box-sizing: border-box;
  margin-bottom: 30rpx;
}
.container .userInfo.data-v-137d5072 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.container .userInfo .avater.data-v-137d5072, .container .userInfo .avater image.data-v-137d5072 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}
.container .userInfo .name.data-v-137d5072 {
  font-size: 35rpx;
  font-weight: bold;
}
.container .infoDetail.data-v-137d5072 {
  width: 100%;
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  box-sizing: border-box;
  background: #fff;
  border-radius: 20rpx;
}
.container .infoDetail .item.data-v-137d5072 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
}

