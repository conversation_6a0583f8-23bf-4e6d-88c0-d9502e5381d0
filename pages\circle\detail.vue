<template>
  <view class="container">
    <view class="headerBox">
      <view class="left">
        <image class="avater" :src="detailInfo.picUrl" />
        <view class="info">
            <view class="name">{{ detailInfo.nickName }}</view>
            <view class="time">{{ detailInfo.createdAt }}</view>
        </view>
      </view>
    </view>

    <view class="detailBox">
      <view class="textBox">
        {{ detailInfo.content }}
      </view>
      <view class="mideo">
        <image v-for="(el,idx) in detailInfo.postMediaList" :key="idx" lazy-load :src="el" mode="widthFix" />
      </view>
    </view>

    <view class="cityInfo">
      <view class="city">{{ detailInfo.city }}</view>
      <!-- <view class="browseNum">
        <u-icon name="eye" color="#999" size="23"></u-icon>
        <text>浏览量</text>
      </view> -->
    </view>

    <view class="commentBox">
      <view class="title">评论({{ detailInfo.comments.length }})</view>
      <view class="commentList">
        <view class="commentItem" v-for="(item, index) in detailInfo.comments" :key="index">
          <view class="com_header">
            <view class="left">
              <image :src="item.picUrl" />
              <text>{{ item.nickName }}</text>
            </view>
            <u-icon class="right" name="more-dot-fill" color="#999" @click="handlerMore(item)"></u-icon>
          </view>

          <view class="com_text">{{ item.content }}</view>

          <view class="com_info">
            <view class="timeCity">五个月前 深圳</view>
            <!-- <view class="like">
              <u-icon name="thumb-up" color="#999" size="18"></u-icon>
              <text>0</text>
            </view> -->
          </view>
        </view>
      </view>
    </view>

    <view class="inputModule">
      <view class="inputBox">
        <input ref="inputRef" id="inputRef" placeholder="说点什么吧..." type="text" v-model="commentContent" class="uni-input" />
      </view>
      <view class="operateBox">
        <view class="sendBtn" @click="addComment">提交</view>

        <view class="iconBox" @click="thumbUpPosts">
          <u-icon name="thumb-up" :color="detailInfo.isLikes? '#feae61': '#999'" size="23"></u-icon>
          <text>{{ detailInfo.likeCount }}</text>
        </view>
      </view>
    </view>

    <u-action-sheet :actions="list" :show="commentStatus" cancelText="取消"  @select="selectClick" @close="commentStatus = false"></u-action-sheet>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { get, post } from '@/utils/request.js'

export default {
  data() {
    return {
        commentStatus: false,
        list: [{
            name:'回复'
        },{
            name:'删除'
        }],
        detailInfo: {
            comments:[]
        },
        commentContent: '',
        operateData: {},
        isReplay: false
    }
  },
  computed: {
        ...mapState(['userInfo']),
    },
  onLoad(options) {
    if(options.id){
        this.handlerGetDetail(options.id)
    }
  },
  methods: {
    handlerGetDetail(id){
        get('/system/wx/posts/getInfo',{ id }).then(res => {
            this.detailInfo = res.data
        }).catch(err => {
            console.log(err)
        })
    },
    handlerMore(item){
        this.operateData = item
        this.commentStatus = true
    },
    selectClick(e){
        switch(e.name){
            case '回复':
                this.replyComment()
                break;
            case '删除':
                this.deleteComment()
                break;
            default:
                break;
        }
      console.log(e)
    },
    replyComment(){
        this.isReplay = true
        uni.createSelectorQuery().select('#inputRef').context((res) => {
            console.log(res)
            res.context.focus()
        }).exec();
    },
    deleteComment(){
        if(this.userInfo.id != this.operateData.userId){
            return uni.showToast({ title: '无权限', icon: 'none' })
        }
        let params = {
            id: this.operateData.id
        }
        post('/system/wx/posts/delComment',params).then((res) => {
            if (res.code == '200') {
                uni.showToast({ title: '删除成功', icon: 'none' })
                this.handlerGetDetail(this.detailInfo.id)
                this.operateData = {}
            }
        })
    },
    thumbUpPosts(){
        let params = {}
        post('/system/wx/posts/likes',params).then((res) => {
            if (res.code == '200') {
                this.$set(this.detailInfo, 'thumbUpNum', this.detailInfo.thumbUpNum + 1)
            }
        })
    },
    addComment(){
        if(this.commentContent.trim() == '') {
            return uni.showToast({ title: '请输入评论内容', icon: 'none' })
        }
        let params = {
            postId: this.detailInfo.id,
            content: this.commentContent,
        }
        if(this.isReplay){
            params.parentId = this.operateData.id
        }
        post('/system/wx/posts/addComment', params).then(res => {
            uni.showToast({ title: '评论成功', icon: 'none' })
            this.commentContent = ''
            this.handlerGetDetail(this.detailInfo.id)
        }).finally(() => {
            this.isReplay = false
        })
    }
  },
}
</script>

<style lang="scss" scoped>
  .container{
    width: 100%;
    min-height: 100vh;
    padding: 30rpx 30rpx 130rpx;
    box-sizing: border-box;
    background: #f5f5f5;

    .headerBox{
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30rpx;

      .left {
            flex: 1;
          display: flex;
          align-items: center;
          gap: 10rpx;
          overflow: hidden;
        }
        .left .avater {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          flex-shrink: 0;
        }
        .left .info{
            flex: 1;
            overflow: hidden;
        }
        .left .info .name {
            flex: 1;
            font-size: 35rpx;
            color: #020202;
            line-height: 45rpx;
            font-weight: 500;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
        .left .info .time {
          font-size: 28rpx;
          color: #999;
          line-height: 41rpx;
        }

        .right{
            flex-shrink: 0;
        }
    }

    .detailBox{
      width: 100%;
      margin-top: 30rpx;
      .textBox{
        font-size: 30rpx;
        color: #020202;
        line-height: 45rpx;
        white-space: wrap;
        margin-bottom: 20rpx;
      }

      .mideo{
        width: 100%;
        display: flex;
        gap: 20rpx 20rpx;
        flex-wrap: wrap;

        image{
          flex: 1;
          min-width: 30%;
          border-radius: 16rpx;
        }
      }
    }

    .cityInfo{
      width: 100%;
      display: flex;
      justify-content: space-between;
      color: #999;
      margin: 30rpx 0;
      border-bottom: 1rpx solid #eeeeee;
      padding-bottom: 20rpx;

      .browseNum{
        display: flex;
        gap: 10rpx;
      }
    }

    .commentBox{
      width: 100%;

      .title{
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
      }
      .commentItem{
        margin-bottom: 30rpx;
        padding-bottom: 20rpx;
        border-bottom: 1rpx solid #f3f3f3;
      }

      .commentItem .com_header{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .left{
          display: flex;
          align-items: center;
          gap: 15rpx;
          font-size: 26rpx;
          font-weight: bold;
        }
        .left image{
          width: 55rpx;
          height: 55rpx;
          border-radius: 50%;
        }
      }
      .commentItem .com_text{
        font-size: 28rpx;
        line-height: 35rpx;
      }
      .com_info{
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 26rpx;
        color: #999;
        margin-top: 20rpx;

        .like{
          display: flex;
          align-items: center;
        }
      }
    }

    .inputModule{
      width: 100%;
      height: 130rpx;
      position: fixed;
      bottom: 0rpx;
      left: 0rpx;
      padding: 0 20rpx;
      display: flex;
      gap: 20rpx;
      align-items: center;
      box-sizing: border-box;
      background: #fff;
      
      .inputBox{
        flex: 1;
        display: flex;
        align-items: center;
        gap: 20rpx;

        .uni-input{
          flex: 1;
          height: 70rpx;
          border-radius: 50rpx;
          padding: 0 20rpx;
          box-sizing: border-box;
          background: rgba($color: #000000, $alpha: 0.1);
        }

      }
        .sendBtn{
            flex-shrink: 0;
            padding: 10rpx 15rpx;
            font-size: 30rpx;
            color: #fff;
            background: #f7991e;
            border-radius: 16rpx;
        }
      .operateBox{
        display: flex;
        justify-content: flex-end;
        gap: 30rpx;

        .iconBox{
          display: flex;
          align-items: center;
          font-size: 32rpx;
          color: #999;
          gap: 8rpx;
        }
      }
    }
  }
</style>