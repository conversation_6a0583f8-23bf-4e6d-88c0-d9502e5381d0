<template>
  <view class="container">
    <view class="searchBox">
      <view class="label">当前城市</view>
      <view class="city" @click="cityPickerStatus = true">
        {{ city }}
        <u-icon name="arrow-down-fill"></u-icon>
      </view>
    </view>

    <view class="content">
      <view class="item" v-for="(item, index) in serverSiteList" :key="index" @click="handlerSiteDetail(item)">
        <view class="lable">{{ item.name }}</view>
        <u-icon name="arrow-right"></u-icon>
      </view>
    </view>


      <u-picker :show="cityPickerStatus" :columns="[cityList]" @confirm="confirm" @close="closePicker" @cancel="closePicker" closeOnClickOverlay></u-picker>
  </view>
</template>

<script>
import { get } from '@/utils/request.js'
import { mapState } from 'vuex'
export default {
    data(){
        return {
            serverSiteList: [],
            cityPickerStatus: false,
        }
    },
    computed: {
        ...mapState(['city', 'cityList']),
    },
    onLoad() {
        this.getServerSiteList()
    },
    methods: {
        getServerSiteList(){
            get('/system/wx/siteList').then(res => {
                if(res.code == '200'){
                    this.serverSiteList = res.rows
                }

            }).catch(err => {
                console.log(err)
            })
        },
        handlerSiteDetail(item){
            uni.navigateTo({
                url: '/pages/serverSite/siteDetail?id=' + item.id
            })
        },
        confirm(e){
            let cityName = e.value[0]
            this.$store.commit('setCity', cityName)
            this.cityPickerStatus = false
        },
        closePicker(){
            this.cityPickerStatus = false
        },
    },
}
</script>

<style lang="scss" scoped>
.container{
  width: 100%;
  height: 100vh;
  padding: 30rpx;
  overflow: auto;
  box-sizing: border-box;

  .searchBox{
    height: 80rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #f3f2f2;
    font-weight: bold;

    .city{
      display: flex;
      align-items: center;
    }
  }

  .content .item{
      height: 80rpx;
      display: flex;
      font-size: 30rpx;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1rpx solid #f3f2f2;
    }
}
</style>