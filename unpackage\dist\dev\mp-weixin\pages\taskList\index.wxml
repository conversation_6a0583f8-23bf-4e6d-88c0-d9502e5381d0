<view class="container data-v-2c7bad3f"><u-sticky vue-id="62fa7ba7-1" bgColor="#fff" class="data-v-2c7bad3f" bind:__l="__l" vue-slots="{{['default']}}"><u-tabs vue-id="{{('62fa7ba7-2')+','+('62fa7ba7-1')}}" list="{{list}}" data-event-opts="{{[['^click',[['handlerClick']]]]}}" bind:click="__e" class="data-v-2c7bad3f" bind:__l="__l"></u-tabs></u-sticky><view class="listBox data-v-2c7bad3f"><scroll-view class="scrollBox data-v-2c7bad3f" scroll-y="true" refresher-enabled="{{true}}" refresher-triggered="{{refresherTriggered}}" scroll-top="{{scrollTop}}" refresher-threshold="100" lower-threshold="100" data-event-opts="{{[['refresherpulling',[['handlerRefresher',['$event']]]],['scrolltolower',[['handlerTolower',['$event']]]],['scroll',[['onScroll',['$event']]]]]}}" bindrefresherpulling="__e" bindscrolltolower="__e" bindscroll="__e"><block wx:for="{{taskList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['taskList','',index]]]]]]]}}" class="listItem data-v-2c7bad3f" bindtap="__e"><view class="item data-v-2c7bad3f"><view class="lable data-v-2c7bad3f">车型：</view><view class="value data-v-2c7bad3f">{{item.carType}}</view></view><view class="item data-v-2c7bad3f"><view class="lable data-v-2c7bad3f">手机号：</view><view class="value data-v-2c7bad3f">{{item.phone}}</view></view><view class="item data-v-2c7bad3f"><view class="lable data-v-2c7bad3f">创建时间：</view><view class="value data-v-2c7bad3f">{{item.createTime}}</view></view><block wx:if="{{item.status==0}}"><view class="btnBox data-v-2c7bad3f"><view data-event-opts="{{[['tap',[['receivingOrders',['$0'],[[['taskList','',index]]]]]]]}}" class="btnItem data-v-2c7bad3f" catchtap="__e">接单</view></view></block><view class="{{['data-v-2c7bad3f','status',stateColor[item.status]]}}">{{stateList[item.status]}}</view></view></block><block wx:if="{{$root.g0==0}}"><view class="data-v-2c7bad3f"><u-empty vue-id="62fa7ba7-3" class="data-v-2c7bad3f" bind:__l="__l"></u-empty></view></block></scroll-view></view></view>