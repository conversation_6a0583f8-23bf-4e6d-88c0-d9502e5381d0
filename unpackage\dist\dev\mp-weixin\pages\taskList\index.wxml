<view class="container data-v-2c7bad3f"><u-sticky vue-id="62fa7ba7-1" bgColor="#fff" class="data-v-2c7bad3f" bind:__l="__l" vue-slots="{{['default']}}"><u-tabs vue-id="{{('62fa7ba7-2')+','+('62fa7ba7-1')}}" list="{{list}}" data-event-opts="{{[['^click',[['handlerClick']]]]}}" bind:click="__e" class="data-v-2c7bad3f" bind:__l="__l"></u-tabs></u-sticky><view class="listBox data-v-2c7bad3f"><block wx:for="{{taskList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toDetail',['$0'],[[['taskList','',index]]]]]]]}}" class="listItem data-v-2c7bad3f" bindtap="__e"><view class="item data-v-2c7bad3f"><view class="lable data-v-2c7bad3f">车型：</view><view class="value data-v-2c7bad3f">{{item.carType}}</view></view><view class="item data-v-2c7bad3f"><view class="lable data-v-2c7bad3f">手机号：</view><view class="value data-v-2c7bad3f">{{item.phone}}</view></view><view class="item data-v-2c7bad3f"><view class="lable data-v-2c7bad3f">创建时间：</view><view class="value data-v-2c7bad3f">{{item.createTime}}</view></view><view class="btnBox data-v-2c7bad3f"><view class="data-v-2c7bad3f">抢单</view></view><view class="{{['data-v-2c7bad3f','status',stateColor[item.status]]}}">{{stateList[item.status]}}</view></view></block></view></view>