<template>
	<view class="container">
    <view class="headerBox">
    </view>

    <view class="centerBox">
      <view class="item" @click="handlerClick('/pages/rescue/index', false)">
        <image src="/static/2.png" />
        <text>道路救援</text>
      </view>
      <view class="item" @click="handlerClick('/pages/serverSite/index', true)">
        <image src="/static/9.png" />
        <text>服务站点</text>
      </view>
      <view class="item" @click="handlerClick('/pages/taskList/index', true)">
        <image src="/static/6.png" />
        <text>任务中心</text>
      </view>
    </view>

    <view class="swiperBox">
      <u-swiper :list="list" circular></u-swiper>
    </view>

    <view class="newsBox">
      <u-tabs :list="tabsList" @click="handlerCutNews"></u-tabs>

      <view class="newList">
        <view class="listItem" v-for="(item) in zxList" :key="item.id" @click="handlerDetail(item)">
            <image class="listItemImg" :src="item.slt" mode="scaleToFill" />
            <view class="listItemTitle">{{ item.title }}</view>
        </view>
        <u-empty v-if="!zxList.length"></u-empty>
      </view>
    </view>

    <u-popup :show="loginStatus" round="20" mode="center" @close="loginStatus = false" :safeAreaInsetBottom="false">
      <view class="popContainer">
        <view class="title">手机号授权</view>
        <view class="desc">为了更好为您服务，请授权手机号</view>
        <button class="btn" type="primary" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
            <text>快捷登录</text>
        </button>
      </view>
    </u-popup>

	</view>
</template>

<script>
import { get } from '@/utils/request.js'
import { mapState } from 'vuex'
import uEmpty from '../../uni_modules/uview-ui/components/u-empty/u-empty.vue';
export default {
  components: { uEmpty },
	data() {
		return {
            list:[
                'https://xdmimg.oss-cn-beijing.aliyuncs.com/%E7%B4%A0%E6%9D%90%20%281%29/%209.png',
                'https://xdmimg.oss-cn-beijing.aliyuncs.com/%E7%B4%A0%E6%9D%90%20%281%29/%208.png',
                'https://xdmimg.oss-cn-beijing.aliyuncs.com/%E7%B4%A0%E6%9D%90%20%281%29/%207.png'
            ],
            tabsList: [],
            serverSiteList: [],

            loginStatus: false,
            zxList: [],
		};
	},
    computed: {
        ...mapState(['token', 'userInfo'])
    },
    onLoad(options) {
        this.getZXMap()
        if(this.token){
            this.getUserInfo()
        }
    },
    onShow() {
        this.getCurrentLocation()
    },
	onReady() {
	},
	methods: {
        getUserInfo(){
            get('/system/wx/getWxInfo').then(res => {
                if(res.code == '200'){
                    this.$store.commit('setUserInfo', res.data)
                }
            }).catch(err => {
                console.log(err)
            })
        },
        getPhoneNumber(e) {
            let that = this
            uni.login({
                provider: 'weixin', //使用微信登录
                success: function (loginRes) {
                    let data = {
                        encryptedData: e.detail.encryptedData,
                        iv: e.detail.iv,
                        code: loginRes.code
                    }
                    get('/system/wx/user/initLogin', data).then(res => {
                        if(res.code == '200'){
                            that.$store.commit('setToken', res.data.token)
                            that.$store.commit('setUserInfo', res.data)
                        }
                    }).catch(err => {
                        console.log(err)
                    }).finally(() => {
                        that.loginStatus = false
                    })
                }
            })
            
        },
        getZXMap(){
            get('/system/dict/data/type/sys_zx').then(res => {
                if(res.code == '200'){
                    this.tabsList = res.data.map(el => {
                        return {
                            name: el.dictLabel,
                            id: el.dictValue
                        }
                    })
                    this.getZXList(res.data[0].dictValue)
                }

            })
        },
        getZXList(type){
            get('/system/wx/zxList', { zxType: type }).then(res => {
                this.zxList = res.rows || []
            })
        },
        getCurrentLocation() {
            uni.getLocation({
                type: 'gcj02',
                success: (res) => {
                    // 可以根据经纬度反向解析地址
            this.getCityName({ lat: res.latitude, lng: res.longitude })
                },
                fail: (err) => {
            uni.showToast({
            title: '获取位置失败'
            })
                    console.log('获取位置失败:', err)
                }
            })
        },
        getCityName(data){

            get('/system/wx/getCity',data).then(res => {
                if(res.code == '200'){
                    this.$store.commit('setCity', res.msg)
                }

            }).catch(err => {
                console.log(err)
            })
        },
        handlerClick(url = '', isLogin = false){
            if(isLogin && !this.token){
                return this.loginStatus = true
            }
            if(isLogin && !this.userInfo.jyId){
                return uni.showToast({ title: '仅对内部人员开放' })
            }

            uni.navigateTo({url})
        },
        handlerCutNews(e){
            this.getZXList(e.id)
        },
        handlerDetail(item){
            if(!this.token){
                return this.loginStatus = true
            }
            uni.navigateTo({
                url:'/pages/information/detail?id=' + item.id,
            })
        }

    },
};
</script>

<style scoped lang="scss">
.container{
  width: 100%;
  min-height: 100vh;
  background-color: #ebebee;

  .headerBox{
    width: 100%;
    height: 350rpx;
    background: url('https://xdmimg.oss-cn-beijing.aliyuncs.com/%E9%A6%96%E9%A1%B5%E8%83%8C%E6%99%AF%E5%9B%BE.png') no-repeat;
    background-size: 100% 100%;
  }

  .centerBox{
    width: 700rpx;
    margin: 0 auto;
    padding: 25rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-wrap: wrap;
    box-sizing: border-box;
    background: #fff;
    border-radius: 16rpx;
    gap: 50rpx 30rpx ;

    .item{
      width: 20%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
    }
    .item text{
      line-height: 55rpx;
      font-size: 28rpx;
    }
    .item image{
      width: 60rpx;
      height: 60rpx;
    }
  }

  .swiperBox{
    width: 700rpx;
    border-radius: 16rpx;
    margin: 25rpx auto;
    background: #fff;
  }

  .newsBox{
    width: 700rpx;
    border-radius: 16rpx;
    margin: 25rpx auto;
    background: #fff;

    .newList{
      width: 100%;
      min-height: 300rpx;
      padding: 30rpx;
      box-sizing: border-box;
    }
    .newList .listItem{
        display: flex;
        gap: 15rpx;
        font-size: 700;
        margin-bottom: 10rpx;
        border-radius: 20rpx;
        box-shadow: -1px 2px 9px 2px rgba(119,143,249,0.15); 

        .listItemImg{
            width: 220rpx;
            height: 150rpx;
            border-radius: 20rpx;
            box-sizing: border-box;
            padding: 10rpx 15rpx;
        }
    }
  }
  
  .popContainer{
    width: 400rpx;
    height: 400rpx;
    padding: 30rpx;
    box-sizing: border-box;
    text-align: center;

    .title{
      font-size: 30rpx;
      font-weight: bold;
      margin-bottom: 40rpx;
      color: #333;
    }
    .desc{
      font-size: 28rpx;
      color: #666;
      margin-bottom: 70rpx
    }

    .btn{
        height: 80rpx;
        line-height: 80rpx;
        padding: 0rpx 25rpx;
        background: #ffa826;
        color: #fff;
        border-radius: 30rpx;
    }
  }
}
</style>
