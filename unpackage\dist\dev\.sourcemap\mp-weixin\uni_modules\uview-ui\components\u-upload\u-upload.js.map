{"version": 3, "sources": ["webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-upload/u-upload.vue?d10a", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-upload/u-upload.vue?26c6", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-upload/u-upload.vue?ec07", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-upload/u-upload.vue?0eda", "uni-app:///uni_modules/uview-ui/components/u-upload/u-upload.vue", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-upload/u-upload.vue?d69a", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-upload/u-upload.vue?25b1"], "names": ["name", "mixins", "data", "lists", "isInCount", "watch", "fileList", "immediate", "handler", "methods", "formatFileList", "maxCount", "Object", "isImage", "isVideo", "deletable", "chooseFile", "multiple", "disabled", "capture", "accept", "compressed", "maxDuration", "sizeType", "camera", "then", "catch", "onBeforeRead", "beforeRead", "useBeforeRead", "res", "file", "callback", "ok", "getDetail", "index", "onAfterRead", "maxSize", "afterRead", "deleteItem", "onPreviewImage", "uni", "urls", "current", "fail", "onPreviewVideo", "event", "wx", "sources", "filter", "map", "type", "onClickPreview"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC2M;AAC3M,gBAAgB,4MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,iVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAA6yB,CAAgB,6wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+Hj0B;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhCA,eAiCA;EACAA;EACAC;EACAC;IACA;MAIAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA,qBAEA,KADAJ;QAAAA;QAAAK,WACA,KADAA;MAEA;QAAA,OACAC;UACA;UACAC;UACAC;UACAC;QACA;MAAA,EACA;MACA;MACA;IACA;IACAC;MAAA;MACA,IACAL,WAIA,KAJAA;QACAM,WAGA,KAHAA;QACAd,QAEA,KAFAA;QACAe,WACA,KADAA;MAEA;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA,uBACAP;QACAQ;QACAH;QACAE;QACAE;QACAC;QACAC;QACAC;MACA;QACAb;MACA,GACA,CACAc;QACA;MACA,GACAC;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA,IACAC,aAEA,KAFAA;QACAC,gBACA,KADAA;MAEA;MACA;MACA;QACA;QACAC;MACA;MACA;QACAA;UACA,aACA,cACAlB;YACAmB;UACA;YACAC;cACAC;YACA;UACA,GACA;QACA;MACA;MACA;QACA;MACA;MACA;QACAH;UAAA;QAAA;MACA;QACA;MACA;IACA;IACAI;MACA;QACAlC;QACAmC;MACA;IACA;IACAC;MACA,IACAC,UAEA,KAFAA;QACAC,YACA,KADAA;MAEA,qCACAP;QAAA;MAAA,KACAA;MACA;QACA;UACAA;QACA;QACA;MACA;MACA;QACAO;MACA;MACA;QACAP;MACA;IACA;IACAQ;MACA,WACA,UACA3B;QACAmB;MACA,GACA;IACA;IACA;IACAS;MAAA;MACA;MACAC;QACA;QACAC;UAAA;QAAA;UAAA;QAAA;QACAC;QACAC;UACAH;QACA;MACA;IACA;IACAI;MACA;MACA,IACAV,QACAW,4BADAX;MAEA,IACAhC,QACA,UADAA;MAEA4C;QACAC,eACAC;UAAA;QAAA,GACAC;UAAA,OACAtC;YACAuC;UACA;QAAA,EACA;QACAR;QACAC;UACAH;QACA;MACA;IACA;IACAW;MACA,IACAjB,QACAW,4BADAX;MAEA;MACA,WACA,gBACAvB,8DACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjWA;AAAA;AAAA;AAAA;AAAghD,CAAgB,g5CAAG,EAAC,C;;;;;;;;;;;ACApiD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-upload/u-upload.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-upload.vue?vue&type=template&id=69e2a36e&scoped=true&\"\nvar renderjs\nimport script from \"./u-upload.vue?vue&type=script&lang=js&\"\nexport * from \"./u-upload.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-upload.vue?vue&type=style&index=0&id=69e2a36e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"69e2a36e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-upload/u-upload.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-upload.vue?vue&type=template&id=69e2a36e&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uLoadingIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-loading-icon/u-loading-icon\" */ \"@/uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.$u.addStyle(_vm.customStyle)])\n  var l0 = _vm.previewImage\n    ? _vm.__map(_vm.lists, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 =\n          item.isImage || (item.type && item.type === \"image\")\n            ? _vm.$u.addUnit(_vm.width)\n            : null\n        var g1 =\n          item.isImage || (item.type && item.type === \"image\")\n            ? _vm.$u.addUnit(_vm.height)\n            : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          g1: g1,\n        }\n      })\n    : null\n  var g2 =\n    _vm.isInCount && !(_vm.$slots.default || _vm.$slots.$default)\n      ? _vm.$u.addUnit(_vm.width)\n      : null\n  var g3 =\n    _vm.isInCount && !(_vm.$slots.default || _vm.$slots.$default)\n      ? _vm.$u.addUnit(_vm.height)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        l0: l0,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-upload.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-upload\" :style=\"[$u.addStyle(customStyle)]\">\n\t\t<view class=\"u-upload__wrap\" >\n\t\t\t<template v-if=\"previewImage\">\n\t\t\t\t<view\n\t\t\t\t    class=\"u-upload__wrap__preview\"\n\t\t\t\t    v-for=\"(item, index) in lists\"\n\t\t\t\t    :key=\"index\"\n\t\t\t\t>\n\t\t\t\t\t<image\n\t\t\t\t\t    v-if=\"item.isImage || (item.type && item.type === 'image')\"\n\t\t\t\t\t    :src=\"item.thumb || item.url\"\n\t\t\t\t\t    :mode=\"imageMode\"\n\t\t\t\t\t    class=\"u-upload__wrap__preview__image\"\n\t\t\t\t\t    @tap=\"onPreviewImage(item)\"\n\t\t\t\t\t\t:style=\"[{\n\t\t\t\t\t\t\twidth: $u.addUnit(width),\n\t\t\t\t\t\t\theight: $u.addUnit(height)\n\t\t\t\t\t\t}]\"\n\t\t\t\t\t/>\n\t\t\t\t\t<view\n\t\t\t\t\t    v-else\n\t\t\t\t\t    class=\"u-upload__wrap__preview__other\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<u-icon\n\t\t\t\t\t\t    color=\"#80CBF9\"\n\t\t\t\t\t\t    size=\"26\"\n\t\t\t\t\t\t    :name=\"item.isVideo || (item.type && item.type === 'video') ? 'movie' : 'folder'\"\n\t\t\t\t\t\t></u-icon>\n\t\t\t\t\t\t<text class=\"u-upload__wrap__preview__other__text\">{{item.isVideo || (item.type && item.type === 'video') ? '视频' : '文件'}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view\n\t\t\t\t\t    class=\"u-upload__status\"\n\t\t\t\t\t    v-if=\"item.status === 'uploading' || item.status === 'failed'\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"u-upload__status__icon\">\n\t\t\t\t\t\t\t<u-icon\n\t\t\t\t\t\t\t    v-if=\"item.status === 'failed'\"\n\t\t\t\t\t\t\t    name=\"close-circle\"\n\t\t\t\t\t\t\t    color=\"#ffffff\"\n\t\t\t\t\t\t\t    size=\"25\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t<u-loading-icon\n\t\t\t\t\t\t\t    size=\"22\"\n\t\t\t\t\t\t\t    mode=\"circle\"\n\t\t\t\t\t\t\t    color=\"#ffffff\"\n\t\t\t\t\t\t\t    v-else\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text\n\t\t\t\t\t\t    v-if=\"item.message\"\n\t\t\t\t\t\t    class=\"u-upload__status__message\"\n\t\t\t\t\t\t>{{ item.message }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view\n\t\t\t\t\t    class=\"u-upload__deletable\"\n\t\t\t\t\t    v-if=\"item.status !== 'uploading' && (deletable || item.deletable)\"\n\t\t\t\t\t    @tap.stop=\"deleteItem(index)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"u-upload__deletable__icon\">\n\t\t\t\t\t\t\t<u-icon\n\t\t\t\t\t\t\t    name=\"close\"\n\t\t\t\t\t\t\t    color=\"#ffffff\"\n\t\t\t\t\t\t\t    size=\"10\"\n\t\t\t\t\t\t\t></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view\n\t\t\t\t\t    class=\"u-upload__success\"\n\t\t\t\t\t    v-if=\"item.status === 'success'\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<!-- #ifdef APP-NVUE -->\n\t\t\t\t\t\t<image\n\t\t\t\t\t\t    :src=\"successIcon\"\n\t\t\t\t\t\t    class=\"u-upload__success__icon\"\n\t\t\t\t\t\t></image>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t<!-- #ifndef APP-NVUE -->\n\t\t\t\t\t\t<view class=\"u-upload__success__icon\">\n\t\t\t\t\t\t\t<u-icon\n\t\t\t\t\t\t\t    name=\"checkmark\"\n\t\t\t\t\t\t\t    color=\"#ffffff\"\n\t\t\t\t\t\t\t    size=\"12\"\n\t\t\t\t\t\t\t></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t</template>\n\t\t\t\n\t\t\t<template v-if=\"isInCount\">\n\t\t\t\t<view\n\t\t\t\t    v-if=\"$slots.default || $slots.$default\"\n\t\t\t\t    @tap=\"chooseFile\"\n\t\t\t\t>\n\t\t\t\t\t<slot />\n\t\t\t\t</view>\n\t\t\t\t<view\n\t\t\t\t    v-else\n\t\t\t\t    class=\"u-upload__button\"\n\t\t\t\t    :hover-class=\"!disabled ? 'u-upload__button--hover' : ''\"\n\t\t\t\t    hover-stay-time=\"150\"\n\t\t\t\t    @tap=\"chooseFile\"\n\t\t\t\t    :class=\"[disabled && 'u-upload__button--disabled']\"\n\t\t\t\t\t:style=\"[{\n\t\t\t\t\t\twidth: $u.addUnit(width),\n\t\t\t\t\t\theight: $u.addUnit(height)\n\t\t\t\t\t}]\"\n\t\t\t\t>\n\t\t\t\t\t<u-icon\n\t\t\t\t\t    :name=\"uploadIcon\"\n\t\t\t\t\t    size=\"26\"\n\t\t\t\t\t    :color=\"uploadIconColor\"\n\t\t\t\t\t></u-icon>\n\t\t\t\t\t<text\n\t\t\t\t\t    v-if=\"uploadText\"\n\t\t\t\t\t    class=\"u-upload__button__text\"\n\t\t\t\t\t>{{ uploadText }}</text>\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t</view>\n\n\t</view>\n</template>\n\n<script>\n\timport {\n\t\tchooseFile\n\t} from './utils';\n\timport mixin from './mixin.js';\n\timport props from './props.js';\n\n\t/**\n\t * upload 上传\n\t * @description 该组件用于上传图片场景\n\t * @tutorial https://uviewui.com/components/upload.html\n\t * @property {String}\t\t\taccept\t\t\t\t接受的文件类型, 可选值为all media image file video （默认 'image' ）\n\t * @property {String | Array}\tcapture\t\t\t\t图片或视频拾取模式，当accept为image类型时设置capture可选额外camera可以直接调起摄像头（默认 ['album', 'camera'] ）\n\t * @property {Boolean}\t\t\tcompressed\t\t\t当accept为video时生效，是否压缩视频，默认为true（默认 true ）\n\t * @property {String}\t\t\tcamera\t\t\t\t当accept为video时生效，可选值为back或front（默认 'back' ）\n\t * @property {Number}\t\t\tmaxDuration\t\t\t当accept为video时生效，拍摄视频最长拍摄时间，单位秒（默认 60 ）\n\t * @property {String}\t\t\tuploadIcon\t\t\t上传区域的图标，只能内置图标（默认 'camera-fill' ）\n\t * @property {String}\t\t\tuploadIconColor\t\t上传区域的图标的字体颜色，只能内置图标（默认 #D3D4D6 ）\n\t * @property {Boolean}\t\t\tuseBeforeRead\t\t是否开启文件读取前事件（默认 false ）\n\t * @property {Boolean}\t\t\tpreviewFullImage\t是否显示组件自带的图片预览功能（默认 true ）\n\t * @property {String | Number}\tmaxCount\t\t\t最大上传数量（默认 52 ）\n\t * @property {Boolean}\t\t\tdisabled\t\t\t是否启用（默认 false ）\n\t * @property {String}\t\t\timageMode\t\t\t预览上传的图片时的裁剪模式，和image组件mode属性一致（默认 'aspectFill' ）\n\t * @property {String}\t\t\tname\t\t\t\t标识符，可以在回调函数的第二项参数中获取\n\t * @property {Array}\t\t\tsizeType\t\t\t所选的图片的尺寸, 可选值为original compressed（默认 ['original', 'compressed'] ）\n\t * @property {Boolean}\t\t\tmultiple\t\t\t是否开启图片多选，部分安卓机型不支持 （默认 false ）\n\t * @property {Boolean}\t\t\tdeletable\t\t\t是否展示删除按钮（默认 true ）\n\t * @property {String | Number}\tmaxSize\t\t\t\t文件大小限制，单位为byte （默认 Number.MAX_VALUE ）\n\t * @property {Array}\t\t\tfileList\t\t\t显示已上传的文件列表\n\t * @property {String}\t\t\tuploadText\t\t\t上传区域的提示文字\n\t * @property {String | Number}\twidth\t\t\t\t内部预览图片区域和选择图片按钮的区域宽度（默认 80 ）\n\t * @property {String | Number}\theight\t\t\t\t内部预览图片区域和选择图片按钮的区域高度（默认 80 ）\n\t * @property {Object}\t\t\tcustomStyle\t\t\t组件的样式，对象形式\n\t * @event {Function} afterRead\t\t读取后的处理函数\n\t * @event {Function} beforeRead\t\t读取前的处理函数\n\t * @event {Function} oversize\t\t文件超出大小限制\n\t * @event {Function} clickPreview\t点击预览图片\n\t * @event {Function} delete \t\t删除图片\n\t * @example <u-upload :action=\"action\" :fileList=\"fileList\" ></u-upload>\n\t */\n\texport default {\n\t\tname: \"u-upload\",\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, mixin,props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tsuccessIcon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAKKADAAQAAAABAAAAKAAAAAB65masAAACP0lEQVRYCc3YXygsURwH8K/dpcWyG3LF5u/6/+dKVylSypuUl6uUPMifKMWL8oKEB1EUT1KeUPdR3uTNUsSLxb2udG/cbvInNuvf2rVnazZ/ZndmZ87snjM1Z+Z3zpzfp9+Z5mEAhlvjRtZgCKs+gnPAOcAkkMOR4jEHfItjDvgRxxSQD8cM0BuOCaAvXNCBQrigAsXgggYUiwsK0B9cwIH+4gIKlIILGFAqLiBAOTjFgXJxigJp4BQD0sIpAqSJow6kjSNAFTnRaHJwLenD6Mud52VQAcrBfTd2oyq+HtGaGGWAcnAVcXWoM3bCZrdi+ncPfaAcXE5UKVpdW/vitGPqqAtn98d0gXJwX7Qp6MmegUYVhvmTIezdmHlxJCjpHRTCFerLkRRu4k0aqdajN3sWOo0BK//msHa+xDuPC/oNFMKRhTtM4xjIX0SCNpXL4+7VIaHuyiWEp2L7ahWLf8fejfPdqPmC3mJicORZUp1CQzm+GiphvljGk+PBvWRbxii+xVTj5M6CiZ/tsDufvaXyxEUDxeLIyvu3m0iOyEFWVAkydcVYdyFrE9tQk9iMq6f/GNlvwt3LjQfh60LUrw9/cFyyMJUW/XkLSNMV4Mi6C5ML+ui4x5ClAX9sB9w0wV6wglJwJCv5fOxcr6EstgbGiEw4XcfUry4cWrcEUW8n+ARKxXEJHhw2WG43UKSvwI/TSZgvl7kh0b3XLZaLEy0QmMgLZAVH7J+ALOE+AVnDvQOyiPMAWcW5gSzjCPAV+78S5WE0GrQAAAAASUVORK5CYII=',\n\t\t\t\t// #endif\n\t\t\t\tlists: [],\n\t\t\t\tisInCount: true,\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\t// 监听文件列表的变化，重新整理内部数据\n\t\t\tfileList: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler() {\n\t\t\t\t\tthis.formatFileList()\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\tmethods: {\n\t\t\tformatFileList() {\n\t\t\t\tconst {\n\t\t\t\t\tfileList = [], maxCount\n\t\t\t\t} = this;\n\t\t\t\tconst lists = fileList.map((item) =>\n\t\t\t\t\tObject.assign(Object.assign({}, item), {\n\t\t\t\t\t\t// 如果item.url为本地选择的blob文件的话，无法判断其为video还是image，此处优先通过accept做判断处理\n\t\t\t\t\t\tisImage: this.accept === 'image' || uni.$u.test.image(item.url || item.thumb),\n\t\t\t\t\t\tisVideo: this.accept === 'video' || uni.$u.test.video(item.url || item.thumb),\n\t\t\t\t\t\tdeletable: typeof(item.deletable) === 'boolean' ? item.deletable : this.deletable,\n\t\t\t\t\t})\n\t\t\t\t);\n\t\t\t\tthis.lists = lists\n\t\t\t\tthis.isInCount = lists.length < maxCount\n\t\t\t},\n\t\t\tchooseFile() {\n\t\t\t\tconst {\n\t\t\t\t\tmaxCount,\n\t\t\t\t\tmultiple,\n\t\t\t\t\tlists,\n\t\t\t\t\tdisabled\n\t\t\t\t} = this;\n\t\t\t\tif (disabled) return;\n\t\t\t\t// 如果用户传入的是字符串，需要格式化成数组\n\t\t\t\tlet capture;\n\t\t\t\ttry {\n\t\t\t\t\tcapture = uni.$u.test.array(this.capture) ? this.capture : this.capture.split(',');\n\t\t\t\t}catch(e) {\n\t\t\t\t\tcapture = [];\n\t\t\t\t}\n\t\t\t\tchooseFile(\n\t\t\t\t\t\tObject.assign({\n\t\t\t\t\t\t\taccept: this.accept,\n\t\t\t\t\t\t\tmultiple: this.multiple,\n\t\t\t\t\t\t\tcapture: capture,\n\t\t\t\t\t\t\tcompressed: this.compressed,\n\t\t\t\t\t\t\tmaxDuration: this.maxDuration,\n\t\t\t\t\t\t\tsizeType: this.sizeType,\n\t\t\t\t\t\t\tcamera: this.camera,\n\t\t\t\t\t\t}, {\n\t\t\t\t\t\t\tmaxCount: maxCount - lists.length,\n\t\t\t\t\t\t})\n\t\t\t\t\t)\n\t\t\t\t\t.then((res) => {\n\t\t\t\t\t\tthis.onBeforeRead(multiple ? res : res[0]);\n\t\t\t\t\t})\n\t\t\t\t\t.catch((error) => {\n\t\t\t\t\t\tthis.$emit('error', error);\n\t\t\t\t\t});\n\t\t\t},\n\t\t\t// 文件读取之前\n\t\t\tonBeforeRead(file) {\n\t\t\t\tconst {\n\t\t\t\t\tbeforeRead,\n\t\t\t\t\tuseBeforeRead,\n\t\t\t\t} = this;\n\t\t\t\tlet res = true\n\t\t\t\t// beforeRead是否为一个方法\n\t\t\t\tif (uni.$u.test.func(beforeRead)) {\n\t\t\t\t\t// 如果用户定义了此方法，则去执行此方法，并传入读取的文件回调\n\t\t\t\t\tres = beforeRead(file, this.getDetail());\n\t\t\t\t}\n\t\t\t\tif (useBeforeRead) {\n\t\t\t\t\tres = new Promise((resolve, reject) => {\n\t\t\t\t\t\tthis.$emit(\n\t\t\t\t\t\t\t'beforeRead',\n\t\t\t\t\t\t\tObject.assign(Object.assign({\n\t\t\t\t\t\t\t\tfile\n\t\t\t\t\t\t\t}, this.getDetail()), {\n\t\t\t\t\t\t\t\tcallback: (ok) => {\n\t\t\t\t\t\t\t\t\tok ? resolve() : reject();\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tif (!res) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (uni.$u.test.promise(res)) {\n\t\t\t\t\tres.then((data) => this.onAfterRead(data || file));\n\t\t\t\t} else {\n\t\t\t\t\tthis.onAfterRead(file);\n\t\t\t\t}\n\t\t\t},\n\t\t\tgetDetail(index) {\n\t\t\t\treturn {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tindex: index == null ? this.fileList.length : index,\n\t\t\t\t};\n\t\t\t},\n\t\t\tonAfterRead(file) {\n\t\t\t\tconst {\n\t\t\t\t\tmaxSize,\n\t\t\t\t\tafterRead\n\t\t\t\t} = this;\n\t\t\t\tconst oversize = Array.isArray(file) ?\n\t\t\t\t\tfile.some((item) => item.size > maxSize) :\n\t\t\t\t\tfile.size > maxSize;\n\t\t\t\tif (oversize) {\n\t\t\t\t\tthis.$emit('oversize', Object.assign({\n\t\t\t\t\t\tfile\n\t\t\t\t\t}, this.getDetail()));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (typeof afterRead === 'function') {\n\t\t\t\t\tafterRead(file, this.getDetail());\n\t\t\t\t}\n\t\t\t\tthis.$emit('afterRead', Object.assign({\n\t\t\t\t\tfile\n\t\t\t\t}, this.getDetail()));\n\t\t\t},\n\t\t\tdeleteItem(index) {\n\t\t\t\tthis.$emit(\n\t\t\t\t\t'delete',\n\t\t\t\t\tObject.assign(Object.assign({}, this.getDetail(index)), {\n\t\t\t\t\t\tfile: this.fileList[index],\n\t\t\t\t\t})\n\t\t\t\t);\n\t\t\t},\n\t\t\t// 预览图片\n\t\t\tonPreviewImage(item) {\n\t\t\t\tif (!item.isImage || !this.previewFullImage) return\n\t\t\t\tuni.previewImage({\n\t\t\t\t\t// 先filter找出为图片的item，再返回filter结果中的图片url\n\t\t\t\t\turls: this.lists.filter((item) => this.accept === 'image' || uni.$u.test.image(item.url || item.thumb)).map((item) => item.url || item.thumb),\n\t\t\t\t\tcurrent: item.url || item.thumb,\n\t\t\t\t\tfail() {\n\t\t\t\t\t\tuni.$u.toast('预览图片失败')\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t},\n\t\t\tonPreviewVideo(event) {\n\t\t\t\tif (!this.data.previewFullImage) return;\n\t\t\t\tconst {\n\t\t\t\t\tindex\n\t\t\t\t} = event.currentTarget.dataset;\n\t\t\t\tconst {\n\t\t\t\t\tlists\n\t\t\t\t} = this.data;\n\t\t\t\twx.previewMedia({\n\t\t\t\t\tsources: lists\n\t\t\t\t\t\t.filter((item) => isVideoFile(item))\n\t\t\t\t\t\t.map((item) =>\n\t\t\t\t\t\t\tObject.assign(Object.assign({}, item), {\n\t\t\t\t\t\t\t\ttype: 'video'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t),\n\t\t\t\t\tcurrent: index,\n\t\t\t\t\tfail() {\n\t\t\t\t\t\tuni.$u.toast('预览视频失败')\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t},\n\t\t\tonClickPreview(event) {\n\t\t\t\tconst {\n\t\t\t\t\tindex\n\t\t\t\t} = event.currentTarget.dataset;\n\t\t\t\tconst item = this.data.lists[index];\n\t\t\t\tthis.$emit(\n\t\t\t\t\t'clickPreview',\n\t\t\t\t\tObject.assign(Object.assign({}, item), this.getDetail(index))\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import '../../libs/css/components.scss';\n\t$u-upload-preview-border-radius: 2px !default;\n\t$u-upload-preview-margin: 0 8px 8px 0 !default;\n\t$u-upload-image-width:80px !default;\n\t$u-upload-image-height:$u-upload-image-width;\n\t$u-upload-other-bgColor: rgb(242, 242, 242) !default;\n\t$u-upload-other-flex:1 !default;\n\t$u-upload-text-font-size:11px !default;\n\t$u-upload-text-color:$u-tips-color !default;\n\t$u-upload-text-margin-top:2px !default;\n\t$u-upload-deletable-right:0 !default;\n\t$u-upload-deletable-top:0 !default;\n\t$u-upload-deletable-bgColor:rgb(55, 55, 55) !default;\n\t$u-upload-deletable-height:14px !default;\n\t$u-upload-deletable-width:$u-upload-deletable-height;\n\t$u-upload-deletable-boder-bottom-left-radius:100px !default;\n\t$u-upload-deletable-zIndex:3 !default;\n\t$u-upload-success-bottom:0 !default;\n\t$u-upload-success-right:0 !default;\n\t$u-upload-success-border-style:solid !default;\n\t$u-upload-success-border-top-color:transparent !default;\n\t$u-upload-success-border-left-color:transparent !default;\n\t$u-upload-success-border-bottom-color: $u-success !default;\n\t$u-upload-success-border-right-color:$u-upload-success-border-bottom-color;\n\t$u-upload-success-border-width:9px !default;\n\t$u-upload-icon-top:0px !default;\n\t$u-upload-icon-right:0px !default;\n\t$u-upload-icon-h5-top:1px !default;\n\t$u-upload-icon-h5-right:0 !default;\n\t$u-upload-icon-width:16px !default;\n\t$u-upload-icon-height:$u-upload-icon-width;\n\t$u-upload-success-icon-bottom:-10px !default;\n\t$u-upload-success-icon-right:-10px !default;\n\t$u-upload-status-right:0 !default;\n\t$u-upload-status-left:0 !default;\n\t$u-upload-status-bottom:0 !default;\n\t$u-upload-status-top:0 !default;\n\t$u-upload-status-bgColor:rgba(0, 0, 0, 0.5) !default;\n\t$u-upload-status-icon-Zindex:1 !default;\n\t$u-upload-message-font-size:12px !default;\n\t$u-upload-message-color:#FFFFFF !default;\n\t$u-upload-message-margin-top:5px !default;\n\t$u-upload-button-width:80px !default;\n\t$u-upload-button-height:$u-upload-button-width;\n\t$u-upload-button-bgColor:rgb(244, 245, 247) !default;\n\t$u-upload-button-border-radius:2px !default;\n\t$u-upload-botton-margin: 0 8px 8px 0 !default;\n\t$u-upload-text-font-size:11px !default;\n\t$u-upload-text-color:$u-tips-color !default;\n\t$u-upload-text-margin-top: 2px !default;\n\t$u-upload-hover-bgColor:rgb(230, 231, 233) !default;\n\t$u-upload-disabled-opacity:.5 !default;\n\n\t.u-upload {\n\t\t@include flex(column);\n\t\tflex: 1;\n\n\t\t&__wrap {\n\t\t\t@include flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tflex: 1;\n\n\t\t\t&__preview {\n\t\t\t\tborder-radius: $u-upload-preview-border-radius;\n\t\t\t\tmargin: $u-upload-preview-margin;\n\t\t\t\tposition: relative;\n\t\t\t\toverflow: hidden;\n\t\t\t\t@include flex;\n\n\t\t\t\t&__image {\n\t\t\t\t\twidth: $u-upload-image-width;\n\t\t\t\t\theight: $u-upload-image-height;\n\t\t\t\t}\n\n\t\t\t\t&__other {\n\t\t\t\t\twidth: $u-upload-image-width;\n\t\t\t\t\theight: $u-upload-image-height;\n\t\t\t\t\tbackground-color: $u-upload-other-bgColor;\n\t\t\t\t\tflex: $u-upload-other-flex;\n\t\t\t\t\t@include flex(column);\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t&__text {\n\t\t\t\t\t\tfont-size: $u-upload-text-font-size;\n\t\t\t\t\t\tcolor: $u-upload-text-color;\n\t\t\t\t\t\tmargin-top: $u-upload-text-margin-top;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&__deletable {\n\t\t\tposition: absolute;\n\t\t\ttop: $u-upload-deletable-top;\n\t\t\tright: $u-upload-deletable-right;\n\t\t\tbackground-color: $u-upload-deletable-bgColor;\n\t\t\theight: $u-upload-deletable-height;\n\t\t\twidth: $u-upload-deletable-width;\n\t\t\t@include flex;\n\t\t\tborder-bottom-left-radius: $u-upload-deletable-boder-bottom-left-radius;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tz-index: $u-upload-deletable-zIndex;\n\n\t\t\t&__icon {\n\t\t\t\tposition: absolute;\n\t\t\t\ttransform: scale(0.7);\n\t\t\t\ttop: $u-upload-icon-top;\n\t\t\t\tright: $u-upload-icon-right;\n\t\t\t\t/* #ifdef H5 */\n\t\t\t\ttop: $u-upload-icon-h5-top;\n\t\t\t\tright: $u-upload-icon-h5-right;\n\t\t\t\t/* #endif */\n\t\t\t}\n\t\t}\n\n\t\t&__success {\n\t\t\tposition: absolute;\n\t\t\tbottom: $u-upload-success-bottom;\n\t\t\tright: $u-upload-success-right;\n\t\t\t@include flex;\n\t\t\t// 由于weex(nvue)为阿里巴巴的KPI(部门业绩考核)的laji产物，不支持css绘制三角形\n\t\t\t// 所以在nvue下使用图片，非nvue下使用css实现\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tborder-style: $u-upload-success-border-style;\n\t\t\tborder-top-color: $u-upload-success-border-top-color;\n\t\t\tborder-left-color: $u-upload-success-border-left-color;\n\t\t\tborder-bottom-color: $u-upload-success-border-bottom-color;\n\t\t\tborder-right-color: $u-upload-success-border-right-color;\n\t\t\tborder-width: $u-upload-success-border-width;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\t/* #endif */\n\n\t\t\t&__icon {\n\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\tposition: absolute;\n\t\t\t\ttransform: scale(0.7);\n\t\t\t\tbottom: $u-upload-success-icon-bottom;\n\t\t\t\tright: $u-upload-success-icon-right;\n\t\t\t\t/* #endif */\n\t\t\t\t/* #ifdef APP-NVUE */\n\t\t\t\twidth: $u-upload-icon-width;\n\t\t\t\theight: $u-upload-icon-height;\n\t\t\t\t/* #endif */\n\t\t\t}\n\t\t}\n\n\t\t&__status {\n\t\t\tposition: absolute;\n\t\t\ttop: $u-upload-status-top;\n\t\t\tbottom: $u-upload-status-bottom;\n\t\t\tleft: $u-upload-status-left;\n\t\t\tright: $u-upload-status-right;\n\t\t\tbackground-color: $u-upload-status-bgColor;\n\t\t\t@include flex(column);\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\n\t\t\t&__icon {\n\t\t\t\tposition: relative;\n\t\t\t\tz-index: $u-upload-status-icon-Zindex;\n\t\t\t}\n\n\t\t\t&__message {\n\t\t\t\tfont-size: $u-upload-message-font-size;\n\t\t\t\tcolor: $u-upload-message-color;\n\t\t\t\tmargin-top: $u-upload-message-margin-top;\n\t\t\t}\n\t\t}\n\n\t\t&__button {\n\t\t\t@include flex(column);\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\twidth: $u-upload-button-width;\n\t\t\theight: $u-upload-button-height;\n\t\t\tbackground-color: $u-upload-button-bgColor;\n\t\t\tborder-radius: $u-upload-button-border-radius;\n\t\t\tmargin: $u-upload-botton-margin;\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tbox-sizing: border-box;\n\t\t\t/* #endif */\n\n\t\t\t&__text {\n\t\t\t\tfont-size: $u-upload-text-font-size;\n\t\t\t\tcolor: $u-upload-text-color;\n\t\t\t\tmargin-top: $u-upload-text-margin-top;\n\t\t\t}\n\n\t\t\t&--hover {\n\t\t\t\tbackground-color: $u-upload-hover-bgColor;\n\t\t\t}\n\n\t\t\t&--disabled {\n\t\t\t\topacity: $u-upload-disabled-opacity;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-upload.vue?vue&type=style&index=0&id=69e2a36e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-upload.vue?vue&type=style&index=0&id=69e2a36e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756609795236\n      var cssReload = require(\"D:/soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}