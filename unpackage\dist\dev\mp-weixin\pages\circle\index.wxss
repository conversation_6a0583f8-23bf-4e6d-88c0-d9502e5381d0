@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-92537b68 {
  width: 100%;
  height: 100vh;
  padding: 0rpx 25rpx;
  overflow: hidden;
  box-sizing: border-box;
  background: linear-gradient(to bottom, #fcc7ba 0%, #ffffff 30%);
}
.container .navbarBox.data-v-92537b68 {
  display: flex;
  align-items: center;
  gap: 30rpx;
}
.container .navbarBox .release.data-v-92537b68,
.container .navbarBox .city.data-v-92537b68 {
  display: flex;
  gap: 10rpx;
  padding: 10rpx 30rpx;
  color: #020202;
  background: rgba(255, 255, 255, 0.6);
  border: 1rpx solid #ff9012;
  border-radius: 30rpx;
}
.container .scrollBox.data-v-92537b68 {
  width: 100%;
}
.container .scrollBox .contentBox .item.data-v-92537b68 {
  width: 100%;
  padding: 50rpx 0rpx 30rpx;
  border-bottom: 1rpx solid #ececec;
  box-sizing: border-box;
}
.container .scrollBox .contentBox .item .header.data-v-92537b68 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  gap: 30rpx;
}
.container .scrollBox .contentBox .item .header .left.data-v-92537b68 {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10rpx;
  overflow: hidden;
}
.container .scrollBox .contentBox .item .header .left .avater.data-v-92537b68 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  flex-shrink: 0;
}
.container .scrollBox .contentBox .item .header .left .info.data-v-92537b68 {
  flex: 1;
  overflow: hidden;
}
.container .scrollBox .contentBox .item .header .left .info .name.data-v-92537b68 {
  flex: 1;
  font-size: 35rpx;
  color: #020202;
  line-height: 45rpx;
  font-weight: 500;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.container .scrollBox .contentBox .item .header .left .info .time.data-v-92537b68 {
  font-size: 28rpx;
  color: #999;
  line-height: 41rpx;
}
.container .scrollBox .contentBox .item .header .right.data-v-92537b68 {
  flex-shrink: 0;
}
.container .scrollBox .contentBox .item .content.data-v-92537b68 {
  width: 100%;
}
.container .scrollBox .contentBox .item .content .textBox.data-v-92537b68 {
  font-size: 30rpx;
  color: #020202;
  line-height: 45rpx;
  white-space: wrap;
  margin-bottom: 20rpx;
}
.container .scrollBox .contentBox .item .content .mideo.data-v-92537b68 {
  width: 100%;
  display: flex;
  gap: 20rpx 20rpx;
  flex-wrap: wrap;
}
.container .scrollBox .contentBox .item .content .mideo image.data-v-92537b68 {
  flex: 1;
  min-width: 30%;
  border-radius: 16rpx;
}
.container .scrollBox .contentBox .item .bottomBtn.data-v-92537b68 {
  display: flex;
  justify-content: flex-end;
  gap: 50rpx;
  margin-top: 30rpx;
}
.container .scrollBox .contentBox .item .bottomBtn .iconBox.data-v-92537b68 {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #999;
  gap: 8rpx;
}
.container .scrollBox .contentBox .item .bottomBtn .iconBox .shareBtn.data-v-92537b68 {
  display: flex;
  font-size: 32rpx;
  color: #999;
  background: transparent;
}
.container .scrollBox .contentBox .item .bottomBtn .iconBox .shareBtn.data-v-92537b68::after {
  display: none;
}
.container .releaseList.data-v-92537b68 {
  padding: 50rpx;
  display: flex;
  gap: 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-sizing: border-box;
}
.container .releaseList .item.data-v-92537b68 {
  width: 90rpx;
  padding: 20rpx 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10rpx;
  font-size: 29rpx;
  font-weight: bold;
  color: #020202;
  background: rgba(197, 196, 196, 0.2);
  border-radius: 10rpx;
}
.container .releaseList .item image.data-v-92537b68 {
  width: 50rpx;
  height: 50rpx;
}

