// 封装请求方法
// const BASE_URL = 'http://8.152.2.247:8081/prod-api' // 请替换为实际的API地址
const BASE_URL = 'https://xdmcyh.com/prod-api' // 请替换为实际的API地址


// 请求拦截器
const request = (options = {}) => {
  return new Promise((resolve, reject) => {
    // 显示加载提示
    // uni.showLoading({
    //   title: '请求中...',
    //   mask: true
    // })

    uni.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        // 可以在这里添加token等认证信息
        'Authorization': 'Bearer ' + uni.getStorageSync('TOKEN'),
        ...options.header
      },
      success: (res) => {
        // uni.hideLoading()
        
        if (res.statusCode === 200) {
          // 根据后端返回的数据结构调整
          if (res.data.code === 200 || res.data.success) {
            resolve(res.data)
          } else {
            uni.showToast({
              title: res.data.message || '请求失败',
              icon: 'none'
            })
            reject(res.data)
          }
        } else {
          uni.showToast({
            title: '网络错误',
            icon: 'none'
          })
          reject(res)
        }
      },
      fail: (err) => {
        uni.hideLoading()
        uni.showToast({
          title: '网络连接失败',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

// GET请求
export const get = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  })
}

// POST请求
export const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

// PUT请求
export const put = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

// DELETE请求
export const del = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}

export default request
