<template>
	<view class="container">
    <u-sticky bgColor="#fff">
      <u-tabs :list="list" @click="handlerClick"></u-tabs>
    </u-sticky>

     <view class="listBox">
        <scroll-view 
            scroll-y="true" 
            class="scrollBox" 
            refresher-enabled
            :refresher-triggered="refresherTriggered"
            @refresherpulling="handlerRefresher" 
            @scrolltolower="handlerTolower"
            @scroll="onScroll"
            :scroll-top	="scrollTop"
            refresher-threshold="100"
            lower-threshold="100"
        >
            <view class="listItem" v-for="(item, index) in taskList" :key="index" @click="toDetail(item)">
                <view class="item">
                    <view class="lable">车型：</view>
                    <view class="value">{{ item.carType }}</view>
                </view>
                <view class="item">
                    <view class="lable">手机号：</view>
                    <view class="value">{{ item.phone }}</view>
                </view>
                <view class="item">
                    <view class="lable">创建时间：</view>
                    <view class="value">{{ item.createTime }}</view>
                </view>

                <view class="btnBox" v-if="item.status == 0">
                    <view class="btnItem" @click.stop="receivingOrders(item)">接单</view>
                </view>

                <view :class="['status',stateColor[item.status]]" >{{ stateList[item.status] }}</view>
            </view>
            <view v-if="taskList.length == 0">
                <u-empty></u-empty>
            </view>
        </scroll-view>
     </view>
  </view>
</template>

<script>
import { get } from '@/utils/request.js'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      list: [{
        name: '待分配',
        value: 0
      }, {
        name: '处理中',
        value: 1
      }, {
        name: '处理完成',
        value: 2
      }],
      taskList: [],
      current: 0,
      stateList: {
        0:'待分配',
        1:'处理中',
        2:'已完成',
      },
      stateColor:{
        0:'',
        1:'statusProcessing',
        2:'statusSuccess',
      },
      refresherTriggered: false,
      dataEnd: false,
      params: {
        pageNum: 1,
        pageSize: 20,
        id: '',
        status: 0
      },
      scrollTop: 0,
      oldScrollTop: 0
    }
  },
  computed: {
    ...mapState(['userInfo'])
  },
  onLoad(options) {
    this.params.id = this.userInfo.jyId
    this.getList()
  },
  methods: {
    onScroll(e){
        this.oldScrollTop = e.detail.scrollTop
    },
    toDetail(item){
      uni.navigateTo({
        url: `/pages/taskList/detail?id=${item.id}`
      })
    },
    handlerClick(e){
        console.log(e)
        this.scrollTop = this.oldScrollTop
        this.current = e.index
        this.params.status = this.list[e.index].value
        this.getList()

        this.$nextTick(() => {
            this.scrollTop = 0
        })
    },
    getList(){
        uni.showLoading({
            title: '加载中...',
            icon: 'none'
        })
        get('/system/wx/list', this.params).then(res => {
            if (this.params.pageNum == 1) {
                this.taskList = res.data || []
            }else{
                this.taskList = this.taskList.concat(res.data || [])
            }
            if(res.data?.length < 20){
                this.dataEnd = true
            }else{
                this.dataEnd = false
            }
        }).finally(() => {
            setTimeout(() => {
                this.refresherTriggered = false
            }, 1000);
            uni.hideLoading()
        })
    },
    handlerRefresher () { 
        if(this.refresherTriggered) return
        this.refresherTriggered = true
        this.params.pageNum = 1
        this.getList()
    },
    handlerTolower () {
        if(!this.dataEnd){
            this.params.pageNum += 1
            this.getList()
        }
    },
    receivingOrders(item){
        let that = this
        uni.showModal({
            title: '提示',
            content: '是否接单',
            success: function (res) {
              if (res.confirm) {
                get('/system/wx/jd',{id: item.id}).then(res => {
                    if(res.code == '200'){
                        uni.showToast({
                            title: '接单成功',
                            icon: 'none'
                        })
                        setTimeout(() => {
                            that.params.pageNum = 1
                            that.getList()
                        }, 1500);
                    }
                }).catch(err => {
                    console.log(err)
                })
              }
            }
        });
    }
  }

}
</script>

<style scoped lang="scss">
.container{
  width: 1750rpx;
  min-height: 100vh;
  background: #f8f8f8;

  .listBox{
    width: 750rpx;
    padding: 30rpx;
    box-sizing: border-box;
    overflow: hidden;

    .scrollBox{
        width: 100%;
        height: calc(100vh - 88rpx - 60rpx );
    }

    .listItem{
      width: 690rpx;
      padding: 30rpx;
      border-radius: 16rpx;
      margin-bottom: 30rpx;
      background: #fff;
      box-sizing: border-box;
      position: relative;

      .item{
        height: 48rpx;
        display: flex;
        align-items: center;

        .lable{
          width: 170rpx;
          flex-shrink: 0;
        }
        .value{
          flex: 1;
        }
      }

      .btnBox{
        display: flex;
        gap: 20rpx;
        margin-top: 25rpx;

        .btnItem{
            height: 50rpx;
            line-height: 46rpx;
            padding: 0rpx 15rpx;
            text-align: center;
            color: #fff;
            background: #f1a026;
            border-radius: 10rpx;
        }
      }

      .status{
        position: absolute;
        right: 0rpx;
        top: 0rpx;
        font-size: 22rpx;
        padding: 5rpx 8rpx;
        background: #dbdbdb;
        border-radius: 0 16rpx 0rpx 16rpx;
      }

      .statusProcessing{
        color: #fff;
        background: #6bc900;
      }
      .statusSuccess{
        color: #fff;
        background: #0079c9;
      }
    }
  }
}
</style>