<template>
	<view class="container">
    <u-sticky bgColor="#fff">
      <u-tabs :list="list" @click="handlerClick"></u-tabs>
    </u-sticky>

     <view class="listBox">
        <view class="listItem" v-for="(item, index) in taskList" :key="index" @click="toDetail(item)">
          <view class="item">
            <view class="lable">车型：</view>
            <view class="value">{{ item.carType }}</view>
          </view>
          <view class="item">
            <view class="lable">手机号：</view>
            <view class="value">{{ item.phone }}</view>
          </view>
          <view class="item">
            <view class="lable">创建时间：</view>
            <view class="value">{{ item.createTime }}</view>
          </view>

          <view class="btnBox">
            <view>抢单</view>
          </view>

          <view :class="['status',stateColor[item.status]]" >{{ stateList[item.status] }}</view>
        </view>
     </view>
  </view>
</template>

<script>
import { get } from '@/utils/request.js'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      list: [{
        name: '全部',
      }, {
        name: '处理中',
      }, {
        name: '处理完成'
      }],
      taskList: [],
      current: 0,
      stateList: {
        0:'待分配',
        1:'处理中',
        2:'已完成',
      },
      stateColor:{
        0:'',
        1:'statusProcessing',
        2:'statusSuccess',
      }
    }
  },
  computed: {
    ...mapState(['userInfo'])
  },
  onLoad(options) {
    this.getList()
  },
  methods: {
    toDetail(item){
      uni.navigateTo({
        url: `/pages/taskList/detail?id=${item.id}`
      })
    },
    handlerClick(e){
      this.current = e.index
      this.getList()
    },
    getList(){
      let status = this.current? this.current : 3
      let params = {
        id: this.userInfo.jyId || 1,
        status
      }
      get('/system/wx/list', params).then(res => {
        if(res.code == '200'){
          this.taskList = res.data
        }
      }).catch(err => {
        console.log(err)
      })
    }
  }

}
</script>

<style scoped lang="scss">
.container{
  width: 1750rpx;
  min-height: 100vh;
  background: #f8f8f8;

  .listBox{
    width: 750rpx;
    padding: 30rpx;
    box-sizing: border-box;
    overflow: hidden;

    .listItem{
      width: 690rpx;
      padding: 30rpx;
      border-radius: 16rpx;
      margin-bottom: 30rpx;
      background: #fff;
      box-sizing: border-box;
      position: relative;

      .item{
        height: 48rpx;
        display: flex;
        align-items: center;

        .lable{
          width: 170rpx;
          flex-shrink: 0;
        }
        .value{
          flex: 1;
        }
      }

      .status{
        position: absolute;
        right: 0rpx;
        top: 0rpx;
        font-size: 22rpx;
        padding: 5rpx 8rpx;
        background: #dbdbdb;
        border-radius: 0 16rpx 0rpx 16rpx;
      }

      .statusProcessing{
        color: #fff;
        background: #6bc900;
      }
      .statusSuccess{
        color: #fff;
        background: #0079c9;
      }
    }
  }
}
</style>