{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/code/work/rescue/pages/taskList/index.vue?fda8", "webpack:///D:/code/work/rescue/pages/taskList/index.vue?c312", "webpack:///D:/code/work/rescue/pages/taskList/index.vue?0d87", "webpack:///D:/code/work/rescue/pages/taskList/index.vue?cc8b", "uni-app:///pages/taskList/index.vue", "webpack:///D:/code/work/rescue/pages/taskList/index.vue?b4ea", "webpack:///D:/code/work/rescue/pages/taskList/index.vue?05c1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "list", "name", "value", "taskList", "current", "stateList", "stateColor", "refresherTriggered", "dataEnd", "params", "pageNum", "pageSize", "id", "status", "scrollTop", "oldScrollTop", "computed", "onLoad", "methods", "onScroll", "toDetail", "uni", "url", "handlerClick", "console", "getList", "title", "icon", "setTimeout", "handler<PERSON>ef<PERSON>er", "handlerTolower", "receivingOrders", "content", "success", "that"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqM;AACrM,gBAAgB,4MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAA4wB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACgDhyB;AACA;AAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;MACAC;MACAC;MACAC;QACA;QACA;QACA;MACA;MACAC;QACA;QACA;QACA;MACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC,4BACA,kCACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MAAA;MACAC;MACA;MACA;MACA;MACA;MAEA;QACA;MACA;IACA;IACAC;MAAA;MACAJ;QACAK;QACAC;MACA;MACA;QAAA;QACA;UACA;QACA;UACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;QACAC;UACA;QACA;QACAP;MACA;IACA;IACAQ;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACAV;QACAK;QACAM;QACAC;UACA;YACA;cAAArB;YAAA;cACA;gBACAS;kBACAK;kBACAC;gBACA;gBACAC;kBACAM;kBACAA;gBACA;cACA;YACA;cACAV;YACA;UACA;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AChLA;AAAA;AAAA;AAAA;AAAu9C,CAAgB,64CAAG,EAAC,C;;;;;;;;;;;ACA3+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/taskList/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/taskList/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2c7bad3f&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2c7bad3f&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2c7bad3f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/taskList/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2c7bad3f&scoped=true&\"", "var components\ntry {\n  components = {\n    uSticky: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-sticky/u-sticky\" */ \"@/uni_modules/uview-ui/components/u-sticky/u-sticky.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-tabs/u-tabs\" */ \"@/uni_modules/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-empty/u-empty\" */ \"@/uni_modules/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.taskList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n    <u-sticky bgColor=\"#fff\">\r\n      <u-tabs :list=\"list\" @click=\"handlerClick\"></u-tabs>\r\n    </u-sticky>\r\n\r\n     <view class=\"listBox\">\r\n        <scroll-view \r\n            scroll-y=\"true\" \r\n            class=\"scrollBox\" \r\n            refresher-enabled\r\n            :refresher-triggered=\"refresherTriggered\"\r\n            @refresherpulling=\"handlerRefresher\" \r\n            @scrolltolower=\"handlerTolower\"\r\n            @scroll=\"onScroll\"\r\n            :scroll-top\t=\"scrollTop\"\r\n            refresher-threshold=\"100\"\r\n            lower-threshold=\"100\"\r\n        >\r\n            <view class=\"listItem\" v-for=\"(item, index) in taskList\" :key=\"index\" @click=\"toDetail(item)\">\r\n                <view class=\"item\">\r\n                    <view class=\"lable\">车型：</view>\r\n                    <view class=\"value\">{{ item.carType }}</view>\r\n                </view>\r\n                <view class=\"item\">\r\n                    <view class=\"lable\">手机号：</view>\r\n                    <view class=\"value\">{{ item.phone }}</view>\r\n                </view>\r\n                <view class=\"item\">\r\n                    <view class=\"lable\">创建时间：</view>\r\n                    <view class=\"value\">{{ item.createTime }}</view>\r\n                </view>\r\n\r\n                <view class=\"btnBox\" v-if=\"item.status == 0\">\r\n                    <view class=\"btnItem\" @click.stop=\"receivingOrders(item)\">接单</view>\r\n                </view>\r\n\r\n                <view :class=\"['status',stateColor[item.status]]\" >{{ stateList[item.status] }}</view>\r\n            </view>\r\n            <view v-if=\"taskList.length == 0\">\r\n                <u-empty></u-empty>\r\n            </view>\r\n        </scroll-view>\r\n     </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { get } from '@/utils/request.js'\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n  data() {\r\n    return {\r\n      list: [{\r\n        name: '待分配',\r\n        value: 0\r\n      }, {\r\n        name: '处理中',\r\n        value: 1\r\n      }, {\r\n        name: '处理完成',\r\n        value: 2\r\n      }],\r\n      taskList: [],\r\n      current: 0,\r\n      stateList: {\r\n        0:'待分配',\r\n        1:'处理中',\r\n        2:'已完成',\r\n      },\r\n      stateColor:{\r\n        0:'',\r\n        1:'statusProcessing',\r\n        2:'statusSuccess',\r\n      },\r\n      refresherTriggered: false,\r\n      dataEnd: false,\r\n      params: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        id: '',\r\n        status: 0\r\n      },\r\n      scrollTop: 0,\r\n      oldScrollTop: 0\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState(['userInfo'])\r\n  },\r\n  onLoad(options) {\r\n    this.params.id = this.userInfo.jyId\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    onScroll(e){\r\n        this.oldScrollTop = e.detail.scrollTop\r\n    },\r\n    toDetail(item){\r\n      uni.navigateTo({\r\n        url: `/pages/taskList/detail?id=${item.id}`\r\n      })\r\n    },\r\n    handlerClick(e){\r\n        console.log(e)\r\n        this.scrollTop = this.oldScrollTop\r\n        this.current = e.index\r\n        this.params.status = this.list[e.index].value\r\n        this.getList()\r\n\r\n        this.$nextTick(() => {\r\n            this.scrollTop = 0\r\n        })\r\n    },\r\n    getList(){\r\n        uni.showLoading({\r\n            title: '加载中...',\r\n            icon: 'none'\r\n        })\r\n        get('/system/wx/list', this.params).then(res => {\r\n            if (this.params.pageNum == 1) {\r\n                this.taskList = res.data || []\r\n            }else{\r\n                this.taskList = this.taskList.concat(res.data || [])\r\n            }\r\n            if(res.data?.length < 20){\r\n                this.dataEnd = true\r\n            }else{\r\n                this.dataEnd = false\r\n            }\r\n        }).finally(() => {\r\n            setTimeout(() => {\r\n                this.refresherTriggered = false\r\n            }, 1000);\r\n            uni.hideLoading()\r\n        })\r\n    },\r\n    handlerRefresher () { \r\n        if(this.refresherTriggered) return\r\n        this.refresherTriggered = true\r\n        this.params.pageNum = 1\r\n        this.getList()\r\n    },\r\n    handlerTolower () {\r\n        if(!this.dataEnd){\r\n            this.params.pageNum += 1\r\n            this.getList()\r\n        }\r\n    },\r\n    receivingOrders(item){\r\n        let that = this\r\n        uni.showModal({\r\n            title: '提示',\r\n            content: '是否接单',\r\n            success: function (res) {\r\n              if (res.confirm) {\r\n                get('/system/wx/jd',{id: item.id}).then(res => {\r\n                    if(res.code == '200'){\r\n                        uni.showToast({\r\n                            title: '接单成功',\r\n                            icon: 'none'\r\n                        })\r\n                        setTimeout(() => {\r\n                            that.params.pageNum = 1\r\n                            that.getList()\r\n                        }, 1500);\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err)\r\n                })\r\n              }\r\n            }\r\n        });\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container{\r\n  width: 1750rpx;\r\n  min-height: 100vh;\r\n  background: #f8f8f8;\r\n\r\n  .listBox{\r\n    width: 750rpx;\r\n    padding: 30rpx;\r\n    box-sizing: border-box;\r\n    overflow: hidden;\r\n\r\n    .scrollBox{\r\n        width: 100%;\r\n        height: calc(100vh - 88rpx - 60rpx );\r\n    }\r\n\r\n    .listItem{\r\n      width: 690rpx;\r\n      padding: 30rpx;\r\n      border-radius: 16rpx;\r\n      margin-bottom: 30rpx;\r\n      background: #fff;\r\n      box-sizing: border-box;\r\n      position: relative;\r\n\r\n      .item{\r\n        height: 48rpx;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .lable{\r\n          width: 170rpx;\r\n          flex-shrink: 0;\r\n        }\r\n        .value{\r\n          flex: 1;\r\n        }\r\n      }\r\n\r\n      .btnBox{\r\n        display: flex;\r\n        gap: 20rpx;\r\n        margin-top: 25rpx;\r\n\r\n        .btnItem{\r\n            height: 50rpx;\r\n            line-height: 46rpx;\r\n            padding: 0rpx 15rpx;\r\n            text-align: center;\r\n            color: #fff;\r\n            background: #f1a026;\r\n            border-radius: 10rpx;\r\n        }\r\n      }\r\n\r\n      .status{\r\n        position: absolute;\r\n        right: 0rpx;\r\n        top: 0rpx;\r\n        font-size: 22rpx;\r\n        padding: 5rpx 8rpx;\r\n        background: #dbdbdb;\r\n        border-radius: 0 16rpx 0rpx 16rpx;\r\n      }\r\n\r\n      .statusProcessing{\r\n        color: #fff;\r\n        background: #6bc900;\r\n      }\r\n      .statusSuccess{\r\n        color: #fff;\r\n        background: #0079c9;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2c7bad3f&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2c7bad3f&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756734967566\n      var cssReload = require(\"D:/soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}