@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-7d4e74e9, scroll-view.data-v-7d4e74e9, swiper-item.data-v-7d4e74e9 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-keyboard.data-v-7d4e74e9 {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  background-color: #e0e4e6;
  align-items: stretch;
  padding: 6px 0 6px;
}
.u-keyboard__button.data-v-7d4e74e9 {
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex: 1;
}
.u-keyboard__button__inner-wrapper.data-v-7d4e74e9 {
  box-shadow: 0 1px 0px #999992;
  margin: 8rpx 5rpx;
  border-radius: 4px;
}
.u-keyboard__button__inner-wrapper__inner.data-v-7d4e74e9 {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 64rpx;
  background-color: #FFFFFF;
  height: 80rpx;
  border-radius: 4px;
}
.u-keyboard__button__inner-wrapper__inner__text.data-v-7d4e74e9 {
  font-size: 16px;
  color: #303133;
}
.u-keyboard__button__inner-wrapper__left.data-v-7d4e74e9, .u-keyboard__button__inner-wrapper__right.data-v-7d4e74e9 {
  border-radius: 4px;
  width: 134rpx;
  height: 80rpx;
  background-color: #BBBCC6;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  box-shadow: 0 1px 0px #999992;
}
.u-keyboard__button__inner-wrapper__left__line.data-v-7d4e74e9 {
  font-size: 15px;
  color: #303133;
  margin: 0 1px;
}
.u-keyboard__button__inner-wrapper__left__lang.data-v-7d4e74e9 {
  font-size: 16px;
  color: #303133;
}
.u-keyboard__button__inner-wrapper__left__lang--active.data-v-7d4e74e9 {
  color: #3c9cff;
}
.u-hover-class.data-v-7d4e74e9 {
  background-color: #BBBCC6;
}

