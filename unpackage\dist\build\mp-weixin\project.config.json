{"description": "项目配置文件。", "packOptions": {"ignore": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": false, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": false, "useApiHook": false, "useApiHostProcess": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false}, "compileType": "miniprogram", "libVersion": "", "appid": "wxa42d17b8f9e340aa", "projectname": "rescue", "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "miniprogram": {"list": []}}}