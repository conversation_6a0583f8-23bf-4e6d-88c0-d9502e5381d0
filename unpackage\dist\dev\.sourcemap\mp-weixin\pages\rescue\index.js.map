{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/code/work/rescue/pages/rescue/index.vue?68d6", "webpack:///D:/code/work/rescue/pages/rescue/index.vue?02f3", "webpack:///D:/code/work/rescue/pages/rescue/index.vue?270a", "webpack:///D:/code/work/rescue/pages/rescue/index.vue?7808", "uni-app:///pages/rescue/index.vue", "webpack:///D:/code/work/rescue/pages/rescue/index.vue?4706", "webpack:///D:/code/work/rescue/pages/rescue/index.vue?2240"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "bgColor", "form", "carType", "phone", "carNo", "desc", "isTag", "city", "address", "detailAdd", "group", "longitude", "latitude", "rules", "required", "message", "trigger", "pattern", "carTypeColumns", "showCarTypePicker", "showNumberKeyboard", "showLicensePlatePicker", "submitting", "cityPickerStatus", "cityList", "computed", "getCityName", "dict<PERSON><PERSON>l", "onLoad", "onReady", "console", "methods", "getCityList", "handlerPhoneNum", "handlerPhoneNumBackspace", "handlerLicensePlate", "handlerLicensePlateBackspace", "confirmCarType", "selectCity", "chooseLocation", "uni", "success", "fail", "title", "content", "showCancel", "getCurrentLocation", "type", "submitForm", "response", "icon", "setTimeout", "submitRescueRequest", "requestData", "resetForm", "confirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqM;AACrM,gBAAgB,4MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,2UAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrHA;AAAA;AAAA;AAAA;AAA4wB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACgKhyB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;QACAX;UACAY;UACAC;UACAC;QACA;QACAb,QACA;UACAW;UACAC;UACAC;QACA,GACA;UACAC;UACAF;UACAC;QACA,EACA;QACAZ;UACAU;UACAC;UACAC;QACA;QACAT;UACAO;UACAC;UACAC;QACA;QACAV;UACAQ;UACAC;UACAC;QACA;QACAR;UACAM;UACAC;UACAC;QACA;QACAP;UACAK;UACAC;UACAC;QACA;MACA;MACA;MACAE,iBACA,qDACA;MACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;UAAA;QAAA;UAAAC;QAAA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA,4BACA;IACA;IACAC;MACA;IACA;IACAC;MACA,4BACA;IACA;IACA;IACAC;MACA;MACA;IACA;IAEA;IAEAC;MACA;IACA;IAEA;IACAC;MAAA;MACAC;QACAC;UACAX;UACA;UACA;UACA;UACA;QACA;QACAY;UACA;UACAF;YACAG;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACAN;QACAO;QACAN;UACA;UACAX;QACA;QACAY;UACAZ;QACA;MACA;IACA;IAEA;IACAkB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAlB;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAmB;gBAEAT;kBACAG;kBACAO;gBACA;;gBAEA;gBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEArB;gBACAU;kBACAG;kBACAO;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAnD;kBACAC;kBACAC;kBACAC;kBACAC;kBACAE;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA0C;MACA;QACApD;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;IACA;IACA2C;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjZA;AAAA;AAAA;AAAA;AAAu9C,CAAgB,64CAAG,EAAC,C;;;;;;;;;;;ACA3+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/rescue/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/rescue/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=a3dd9962&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=a3dd9962&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a3dd9962\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/rescue/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=a3dd9962&scoped=true&\"", "var components\ntry {\n  components = {\n    \"u-Form\": function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u--form/u--form\" */ \"@/uni_modules/uview-ui/components/u--form/u--form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u--input/u--input\" */ \"@/uni_modules/uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-radio-group/u-radio-group\" */ \"@/uni_modules/uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-radio/u-radio\" */ \"@/uni_modules/uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-picker/u-picker\" */ \"@/uni_modules/uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uKeyboard: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-keyboard/u-keyboard\" */ \"@/uni_modules/uview-ui/components/u-keyboard/u-keyboard.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.getCityName(_vm.form.city)\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showNumberKeyboard = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showLicensePlatePicker = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.showCarTypePicker = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.showNumberKeyboard = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.showNumberKeyboard = false\n    }\n    _vm.e5 = function ($event) {\n      _vm.showNumberKeyboard = false\n    }\n    _vm.e6 = function ($event) {\n      _vm.showLicensePlatePicker = false\n    }\n    _vm.e7 = function ($event) {\n      _vm.showLicensePlatePicker = false\n    }\n    _vm.e8 = function ($event) {\n      _vm.showLicensePlatePicker = false\n    }\n    _vm.e9 = function ($event) {\n      _vm.cityPickerStatus = false\n    }\n    _vm.e10 = function ($event) {\n      _vm.cityPickerStatus = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <view class=\"header\">\r\n      <image\r\n        src=\"https://xdmimg.oss-cn-beijing.aliyuncs.com/%E7%B4%A0%E6%9D%90%20%281%29/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250821212300_690_528.png\"\r\n        mode=\"scaleToFill\"\r\n      />\r\n    </view>\r\n\r\n    <view class=\"formBox\">\r\n      <u--form\r\n        :model=\"form\"\r\n        ref=\"uForm\"\r\n        :rules=\"rules\"\r\n        errorType=\"toast\"\r\n        borderBottom\r\n        labelWidth=\"70\"\r\n      >\r\n        <!-- 车型选择 -->\r\n        <u-form-item label=\"车型\" prop=\"carType\" required>\r\n          <u--input v-model=\"form.carType\" placeholder=\"请选择输入车型\" />\r\n        </u-form-item>\r\n        <!-- 手机号 -->\r\n        <u-form-item\r\n          label=\"手机号\"\r\n          prop=\"phone\"\r\n          required\r\n          @click=\"showNumberKeyboard = true\"\r\n        >\r\n          <u--input\r\n            v-model=\"form.phone\"\r\n            placeholder=\"请输入手机号\"\r\n            :readonly=\"true\"\r\n            maxlength=\"11\"\r\n          />\r\n        </u-form-item>\r\n\r\n        <!-- 车牌号 -->\r\n        <u-form-item\r\n          label=\"车牌号\"\r\n          prop=\"carNo\"\r\n          required\r\n          @click=\"showLicensePlatePicker = true\"\r\n        >\r\n          <u-input\r\n            v-model=\"form.carNo\"\r\n            placeholder=\"请输入车牌号\"\r\n            :readonly=\"true\"\r\n          />\r\n        </u-form-item>\r\n\r\n        <!-- 是否有标 -->\r\n        <u-form-item label=\"是否有标\" prop=\"isTag\" required>\r\n          <u-radio-group v-model=\"form.isTag\" placement=\"row\">\r\n            <u-radio label=\"是\" name=\"1\" style=\"margin-right: 30rpx\"></u-radio>\r\n            <u-radio label=\"否\" name=\"0\"></u-radio>\r\n          </u-radio-group>\r\n        </u-form-item>\r\n\r\n        <!-- 城市 -->\r\n        <u-form-item label=\"城市\" prop=\"city\" required @click=\"selectCity\">\r\n          <u--input\r\n            :value=\"getCityName(form.city)\"\r\n            readonly\r\n            placeholder=\"选择城市\"\r\n            suffix-icon=\"arrow-right\"\r\n          />\r\n        </u-form-item>\r\n\r\n        <!-- 地址 -->\r\n        <u-form-item\r\n          label=\"地址\"\r\n          prop=\"address\"\r\n          required\r\n          @click=\"chooseLocation\"\r\n        >\r\n          <u--input\r\n            v-model=\"form.address\"\r\n            readonly\r\n            placeholder=\"点击获取当前位置\"\r\n            suffix-icon=\"map\"\r\n          />\r\n        </u-form-item>\r\n\r\n        <u-form-item label=\"详细地址\" prop=\"detailAdd\" required>\r\n          <u--input v-model=\"form.detailAdd\" placeholder=\"点击输入详细位置\" />\r\n        </u-form-item>\r\n\r\n        <!-- 几群 -->\r\n        <u-form-item label=\"几群\" prop=\"group\">\r\n          <u--input\r\n            v-model=\"form.group\"\r\n            placeholder=\"所在群\"\r\n            :autoHeight=\"true\"\r\n            maxlength=\"300\"\r\n          />\r\n        </u-form-item>\r\n\r\n        <!-- 救援事项 -->\r\n        <u-form-item label=\"救援事项\" prop=\"desc\">\r\n          <u--input\r\n            v-model=\"form.desc\"\r\n            placeholder=\"请描述需要救援的问题\"\r\n            type=\"textarea\"\r\n            :autoHeight=\"true\"\r\n            maxlength=\"200\"\r\n          />\r\n        </u-form-item>\r\n      </u--form>\r\n\r\n      <!-- 提交按钮 -->\r\n      <view class=\"submit-btn-container\">\r\n        <u-button\r\n          type=\"primary\"\r\n          size=\"large\"\r\n          :loading=\"submitting\"\r\n          @click=\"submitForm\"\r\n          customStyle=\"background: #2d89c5\"\r\n        >\r\n          {{ submitting ? '提交中...' : '立即申请救援' }}\r\n        </u-button>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 车型选择器 -->\r\n    <u-picker\r\n      :show=\"showCarTypePicker\"\r\n      :columns=\"carTypeColumns\"\r\n      @confirm=\"confirmCarType\"\r\n      @cancel=\"showCarTypePicker = false\"\r\n    ></u-picker>\r\n\r\n    <!-- 数字键盘 -->\r\n    <u-keyboard\r\n      :show=\"showNumberKeyboard\"\r\n      mode=\"number\"\r\n      :dotDisabled=\"true\"\r\n      @close=\"showNumberKeyboard = false\"\r\n      @change=\"handlerPhoneNum\"\r\n      @backspace=\"handlerPhoneNumBackspace\"\r\n      @cancel=\"showNumberKeyboard = false\"\r\n      @confirm=\"showNumberKeyboard = false\"\r\n    ></u-keyboard>\r\n\r\n    <!-- 车牌键盘 -->\r\n    <u-keyboard\r\n      :show=\"showLicensePlatePicker\"\r\n      mode=\"car\"\r\n      @close=\"showLicensePlatePicker = false\"\r\n      @change=\"handlerLicensePlate\"\r\n      @backspace=\"handlerLicensePlateBackspace\"\r\n      @cancel=\"showLicensePlatePicker = false\"\r\n      @confirm=\"showLicensePlatePicker = false\"\r\n    ></u-keyboard>\r\n\r\n    <u-picker :show=\"cityPickerStatus\" :columns=\"[cityList]\" @confirm=\"confirm\" @close=\"cityPickerStatus = false\" keyName=\"dictLabel\" @cancel=\"cityPickerStatus = false\" closeOnClickOverlay></u-picker>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { post, get } from '@/utils/request.js'\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n  data() {\r\n    return {\r\n      bgColor: 'transparent',\r\n      // 表单数据\r\n      form: {\r\n        carType: '',\r\n        phone: '',\r\n        carNo: '',\r\n        desc: '',\r\n        isTag: '',\r\n        city: '',\r\n        address: '',\r\n        detailAdd: '',\r\n        group: '',\r\n        longitude: '',\r\n        latitude: '',\r\n      },\r\n      // 表单验证规则\r\n      rules: {\r\n        carType: {\r\n          required: true,\r\n          message: '请输入车型',\r\n          trigger: 'change',\r\n        },\r\n        phone: [\r\n          {\r\n            required: true,\r\n            message: '请输入手机号',\r\n            trigger: 'change',\r\n          },\r\n          {\r\n            pattern: /^1[3-9]\\d{9}$/,\r\n            message: '请输入正确的手机号',\r\n            trigger: 'change',\r\n          },\r\n        ],\r\n        carNo: {\r\n          required: true,\r\n          message: '请输入车牌号',\r\n          trigger: 'change',\r\n        },\r\n        city: {\r\n          required: true,\r\n          message: '请选择城市',\r\n          trigger: 'change',\r\n        },\r\n        isTag: {\r\n          required: true,\r\n          message: '请选择是否有标',\r\n          trigger: 'change',\r\n        },\r\n        address: {\r\n          required: true,\r\n          message: '请选择或输入地址',\r\n          trigger: 'change',\r\n        },\r\n        detailAdd: {\r\n          required: true,\r\n          message: '请输入详细地址',\r\n          trigger: 'change',\r\n        },\r\n      },\r\n      // 车型选项\r\n      carTypeColumns: [\r\n        ['轿车', 'SUV', '面包车', '货车', '客车', '摩托车', '电动车', '其他'],\r\n      ],\r\n      // 控制显示状态\r\n      showCarTypePicker: false,\r\n      showNumberKeyboard: false,\r\n      showLicensePlatePicker: false,\r\n      submitting: false,\r\n\r\n      cityPickerStatus: false,\r\n      cityList: []\r\n    }\r\n  },\r\n  computed: {\r\n    getCityName(city){\r\n        return (city) => {\r\n            let cityName = this.cityList.find(el => el.dictValue == city) || {dictLabel: ''}\r\n            return cityName.dictLabel\r\n        }\r\n    },\r\n  },\r\n    onLoad(options) {\r\n        this.getCityList()\r\n    },\r\n  onReady() {\r\n    //如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。\r\n    console.log(this.rules)\r\n    this.$refs.uForm.setRules(this.rules)\r\n  },\r\n  methods: {\r\n    getCityList () {\r\n        get('/system/dict/data/type/sys_city').then((res) => {\r\n            if (res.code == '200') {\r\n                this.cityList = res.data\r\n            }\r\n        })\r\n    },\r\n    handlerPhoneNum(val) {\r\n      this.form.phone += val\r\n    },\r\n    handlerPhoneNumBackspace() {\r\n      if (this.form.phone.length)\r\n        this.form.phone = this.form.phone.substr(0, this.form.phone.length - 1)\r\n    },\r\n    handlerLicensePlate(val) {\r\n      this.form.carNo += val\r\n    },\r\n    handlerLicensePlateBackspace() {\r\n      if (this.form.carNo.length)\r\n        this.form.carNo = this.form.carNo.substr(0, this.form.carNo.length - 1)\r\n    },\r\n    // 车型选择确认\r\n    confirmCarType(value) {\r\n      this.form.carType = value.value[0]\r\n      this.showCarTypePicker = false\r\n    },\r\n\r\n    // 选择城市\r\n\r\n    selectCity() {\r\n        this.cityPickerStatus = true\r\n    },\r\n\r\n    // 选择位置\r\n    chooseLocation() {\r\n      uni.chooseLocation({\r\n        success: (res) => {\r\n          console.log(res, '获取到的位置')\r\n          // this.form.address = res.address + ' ' + res.name\r\n          this.form.address = res.address\r\n          this.form.longitude = res.longitude\r\n          this.form.latitude = res.latitude\r\n        },\r\n        fail: (err) => {\r\n          // 如果用户拒绝授权，可以手动输入\r\n          uni.showModal({\r\n            title: '提示',\r\n            content: '获取位置失败',\r\n            showCancel: false,\r\n          })\r\n        },\r\n      })\r\n    },\r\n\r\n    // 获取当前位置\r\n    getCurrentLocation() {\r\n      uni.getLocation({\r\n        type: 'gcj02',\r\n        success: (res) => {\r\n          // 可以根据经纬度反向解析地址\r\n          console.log('当前位置:', res)\r\n        },\r\n        fail: (err) => {\r\n          console.log('获取位置失败:', err)\r\n        },\r\n      })\r\n    },\r\n\r\n    // 表单提交\r\n    async submitForm() {\r\n      // 表单验证\r\n      try {\r\n        await this.$refs.uForm.validate()\r\n      } catch (errors) {\r\n        console.log('表单验证失败:', errors)\r\n        return\r\n      }\r\n\r\n      this.submitting = true\r\n\r\n      try {\r\n        // 调用后端接口\r\n        const response = await this.submitRescueRequest()\r\n\r\n        uni.showToast({\r\n          title: '提交成功,稍后会有专人电话联系您！',\r\n          icon: 'none',\r\n        })\r\n\r\n        // 提交成功后可以跳转到结果页面或重置表单\r\n        setTimeout(() => {\r\n          this.resetForm()\r\n        }, 1500)\r\n      } catch (error) {\r\n        console.error('提交失败:', error)\r\n        uni.showToast({\r\n          title: '提交失败，请重试',\r\n          icon: 'none',\r\n        })\r\n      } finally {\r\n        this.submitting = false\r\n      }\r\n    },\r\n\r\n    // 调用后端接口提交救援请求\r\n    async submitRescueRequest() {\r\n      const requestData = {\r\n        carType: this.form.carType,\r\n        phone: this.form.phone,\r\n        carNo: this.form.carNo,\r\n        desc: this.form.desc,\r\n        isTag: this.form.isTag,\r\n        address: this.form.address,\r\n        detailAdd: this.form.detailAdd,\r\n        group: this.form.group,\r\n        longitude: this.form.longitude,\r\n        latitude: this.form.latitude,\r\n      }\r\n\r\n      // 调用封装的post方法\r\n      return await post('/system/wx', requestData)\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.form = {\r\n        carType: '',\r\n        phone: '',\r\n        carNo: '',\r\n        desc: '',\r\n        isTag: '',\r\n        city: '',\r\n        address: '',\r\n        detailAdd: '',\r\n        group: '',\r\n        longitude: '',\r\n        latitude: '',\r\n      }\r\n      this.$refs.uForm.clearValidate()\r\n    },\r\n    confirm(e){\r\n        this.cityPickerStatus = false\r\n        this.form.city = e.value[0].dictValue\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: #01529b;\r\n\r\n  .header {\r\n    width: 100%;\r\n    height: 400rpx;\r\n\r\n    image {\r\n      width: 100%;\r\n      height: 100%;\r\n      z-index: 0;\r\n    }\r\n  }\r\n\r\n  .formBox {\r\n    width: 700rpx;\r\n    margin: 0 auto;\r\n    padding: 20rpx 35rpx;\r\n    box-sizing: border-box;\r\n    border-radius: 20rpx;\r\n    background: #fff;\r\n    z-index: 888;\r\n  }\r\n\r\n  ::v-deep .u-radio {\r\n    margin-right: 30rpx;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=a3dd9962&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=a3dd9962&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756730694168\n      var cssReload = require(\"D:/soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}