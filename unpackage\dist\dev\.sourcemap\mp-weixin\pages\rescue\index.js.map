{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/code/work/rescue/pages/rescue/index.vue?68d6", "webpack:///D:/code/work/rescue/pages/rescue/index.vue?02f3", "webpack:///D:/code/work/rescue/pages/rescue/index.vue?270a", "webpack:///D:/code/work/rescue/pages/rescue/index.vue?7808", "uni-app:///pages/rescue/index.vue", "webpack:///D:/code/work/rescue/pages/rescue/index.vue?4706", "webpack:///D:/code/work/rescue/pages/rescue/index.vue?2240"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "bgColor", "form", "carType", "phone", "carNo", "desc", "isTag", "address", "detailAdd", "group", "longitude", "latitude", "rules", "required", "message", "trigger", "pattern", "carTypeColumns", "showCarTypePicker", "showNumberKeyboard", "showLicensePlatePicker", "submitting", "onReady", "console", "methods", "handlerPhoneNum", "handlerPhoneNumBackspace", "handlerLicensePlate", "handlerLicensePlateBackspace", "confirmCarType", "chooseLocation", "uni", "success", "fail", "title", "content", "showCancel", "getCurrentLocation", "type", "submitForm", "response", "icon", "setTimeout", "submitRescueRequest", "requestData", "resetForm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqM;AACrM,gBAAgB,4MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,2UAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtGA;AAAA;AAAA;AAAA;AAA4wB,CAAgB,0wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACkIhyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;QACAV;UACAW;UACAC;UACAC;QACA;QACAZ,QACA;UACAU;UACAC;UACAC;QACA,GACA;UACAC;UACAF;UACAC;QACA,EACA;QACAX;UACAS;UACAC;UACAC;QACA;QACAT;UACAO;UACAC;UACAC;QACA;QACAR;UACAM;UACAC;UACAC;QACA;QACAP;UACAK;UACAC;UACAC;QACA;MACA;MACA;MACAE,iBACA,qDACA;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACAC;QACAC;UACAT;UACA;UACA;UACA;UACA;QACA;QACAU;UACA;UACAF;YACAG;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAC;MACAN;QACAO;QACAN;UACA;UACAT;QACA;QACAU;UACAV;QACA;MACA;IACA;IAEA;IACAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhB;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAiB;gBAEAT;kBACAG;kBACAO;gBACA;;gBAEA;gBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAnB;gBACAQ;kBACAG;kBACAO;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACA1C;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAkC;MACA;QACA3C;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3UA;AAAA;AAAA;AAAA;AAAu9C,CAAgB,64CAAG,EAAC,C;;;;;;;;;;;ACA3+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/rescue/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/rescue/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=a3dd9962&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=a3dd9962&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a3dd9962\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/rescue/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=a3dd9962&scoped=true&\"", "var components\ntry {\n  components = {\n    \"u-Form\": function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u--form/u--form\" */ \"@/uni_modules/uview-ui/components/u--form/u--form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u--input/u--input\" */ \"@/uni_modules/uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-radio-group/u-radio-group\" */ \"@/uni_modules/uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-radio/u-radio\" */ \"@/uni_modules/uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-picker/u-picker\" */ \"@/uni_modules/uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uKeyboard: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-keyboard/u-keyboard\" */ \"@/uni_modules/uview-ui/components/u-keyboard/u-keyboard.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showNumberKeyboard = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showLicensePlatePicker = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.showCarTypePicker = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.showNumberKeyboard = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.showNumberKeyboard = false\n    }\n    _vm.e5 = function ($event) {\n      _vm.showNumberKeyboard = false\n    }\n    _vm.e6 = function ($event) {\n      _vm.showLicensePlatePicker = false\n    }\n    _vm.e7 = function ($event) {\n      _vm.showLicensePlatePicker = false\n    }\n    _vm.e8 = function ($event) {\n      _vm.showLicensePlatePicker = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n    <view class=\"header\">\r\n      <image src=\"https://xdmimg.oss-cn-beijing.aliyuncs.com/%E7%B4%A0%E6%9D%90%20%281%29/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250821212300_690_528.png\" mode=\"scaleToFill\"/>\r\n    </view>\r\n\r\n    <view class=\"formBox\">\r\n\t\t<u--form :model=\"form\" ref=\"uForm\" :rules=\"rules\" errorType=\"toast\" borderBottom labelWidth=\"70\">\r\n\t\t\t<!-- 车型选择 -->\r\n\t\t\t<u-form-item label=\"车型\" prop=\"carType\" required>\r\n\t\t\t\t<u--input\r\n\t\t\t\t\tv-model=\"form.carType\"\r\n\t\t\t\t\tplaceholder=\"请选择输入车型\"\r\n\t\t\t\t/>\r\n\t\t\t</u-form-item>\r\n\t\t\t<!-- 手机号 -->\r\n\t\t\t\t<u-form-item label=\"手机号\" prop=\"phone\" required @click=\"showNumberKeyboard = true\">\r\n\t\t\t\t\t<u--input\r\n\t\t\t\t\t\tv-model=\"form.phone\"\r\n\t\t\t\t\t\tplaceholder=\"请输入手机号\"\r\n\t\t\t\t\t\t:readonly=\"true\"\r\n\t\t\t\t\t\tmaxlength=\"11\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</u-form-item>\r\n\r\n\t\t\t\t<!-- 车牌号 -->\r\n\t\t\t\t<u-form-item label=\"车牌号\" prop=\"carNo\" required @click=\"showLicensePlatePicker = true\">\r\n\t\t\t\t\t<u-input\r\n\t\t\t\t\t\tv-model=\"form.carNo\"\r\n\t\t\t\t\t\tplaceholder=\"请输入车牌号\"\r\n\t\t\t\t\t\t:readonly=\"true\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</u-form-item>\r\n\r\n\t\t\t\t<!-- 是否有标 -->\r\n\t\t\t\t<u-form-item label=\"是否有标\" prop=\"isTag\" required>\r\n\t\t\t\t\t<u-radio-group v-model=\"form.isTag\" placement=\"row\">\r\n\t\t\t\t\t\t<u-radio label=\"是\" name=\"1\" style=\"margin-right: 30rpx;\"></u-radio>\r\n\t\t\t\t\t\t<u-radio label=\"否\" name=\"0\"></u-radio>\r\n\t\t\t\t\t</u-radio-group>\r\n\t\t\t\t</u-form-item>\r\n\r\n\t\t\t\t<!-- 地址 -->\r\n\t\t\t\t<u-form-item label=\"地址\" prop=\"address\" required @click=\"chooseLocation\">\r\n\t\t\t\t\t<u--input\r\n\t\t\t\t\t\tv-model=\"form.address\"\r\n            readonly\r\n\t\t\t\t\t\tplaceholder=\"点击获取当前位置\"\r\n\t\t\t\t\t\tsuffix-icon=\"map\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</u-form-item>\r\n\r\n        <u-form-item label=\"详细地址\" prop=\"detailAdd\" required>\r\n\t\t\t\t\t<u--input \r\n            v-model=\"form.detailAdd\"\r\n\t\t\t\t\t\tplaceholder=\"点击输入详细位置\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</u-form-item>\r\n\r\n\t\t\t\t<!-- 几群 -->\r\n\t\t\t\t<u-form-item label=\"几群\" prop=\"group\">\r\n\t\t\t\t\t<u--input\r\n\t\t\t\t\t\tv-model=\"form.group\"\r\n\t\t\t\t\t\tplaceholder=\"所在群\"\r\n\t\t\t\t\t\t:autoHeight=\"true\"\r\n\t\t\t\t\t\tmaxlength=\"300\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</u-form-item>\r\n\r\n        <!-- 救援事项 -->\r\n\t\t\t\t<u-form-item label=\"救援事项\" prop=\"desc\">\r\n\t\t\t\t\t<u--input\r\n\t\t\t\t\t\tv-model=\"form.desc\"\r\n\t\t\t\t\t\tplaceholder=\"请描述需要救援的问题\"\r\n\t\t\t\t\t\ttype=\"textarea\"\r\n\t\t\t\t\t\t:autoHeight=\"true\"\r\n\t\t\t\t\t\tmaxlength=\"200\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t</u--form>\r\n\r\n\t\t\t<!-- 提交按钮 -->\r\n\t\t\t<view class=\"submit-btn-container\">\r\n\t\t\t\t<u-button\r\n\t\t\t\t\ttype=\"primary\"\r\n\t\t\t\t\tsize=\"large\"\r\n\t\t\t\t\t:loading=\"submitting\"\r\n\t\t\t\t\t@click=\"submitForm\"\r\n\t\t\t\t\tcustomStyle=\"background: #2d89c5\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{{ submitting ? '提交中...' : '立即申请救援' }}\r\n\t\t\t\t</u-button>\r\n\t\t\t</view>\r\n    </view>\r\n\r\n\r\n    <!-- 车型选择器 -->\r\n\t\t<u-picker\r\n\t\t\t:show=\"showCarTypePicker\"\r\n\t\t\t:columns=\"carTypeColumns\"\r\n\t\t\t@confirm=\"confirmCarType\"\r\n\t\t\t@cancel=\"showCarTypePicker = false\"\r\n\t\t></u-picker>\r\n\r\n\t\t<!-- 数字键盘 -->\r\n\t\t<u-keyboard \r\n\t\t\t:show=\"showNumberKeyboard\" \r\n\t\t\tmode=\"number\" \r\n\t\t\t:dotDisabled=\"true\"\r\n      @close=\"showNumberKeyboard = false\"\r\n\t\t\t@change=\"handlerPhoneNum\"\r\n\t\t\t@backspace=\"handlerPhoneNumBackspace\"\r\n\t\t\t@cancel=\"showNumberKeyboard = false\"\r\n\t\t\t@confirm=\"showNumberKeyboard = false\"\r\n\t\t></u-keyboard>\r\n\r\n\t\t<!-- 车牌键盘 -->\r\n\t\t<u-keyboard \r\n\t\t\t:show=\"showLicensePlatePicker\" \r\n\t\t\tmode=\"car\"\r\n      @close=\"showLicensePlatePicker = false\"\r\n\t\t\t@change=\"handlerLicensePlate\"\r\n\t\t\t@backspace=\"handlerLicensePlateBackspace\"\r\n\t\t\t@cancel=\"showLicensePlatePicker = false\"\r\n\t\t\t@confirm=\"showLicensePlatePicker = false\"\r\n\t\t></u-keyboard>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { post } from '@/utils/request.js'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tbgColor: 'transparent',\r\n\t\t\t// 表单数据\r\n\t\t\tform: {\r\n\t\t\t\tcarType: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tcarNo: '',\r\n\t\t\t\tdesc: '',\r\n\t\t\t\tisTag: '',\r\n\t\t\t\taddress: '',\r\n        detailAdd: '',\r\n\t\t\t\tgroup: '',\r\n        longitude: '',\r\n        latitude: ''\r\n\t\t\t},\r\n\t\t\t// 表单验证规则\r\n\t\t\trules: {\r\n\t\t\t\tcarType: {\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请输入车型',\r\n\t\t\t\t\t\ttrigger: 'change'\r\n        },\r\n\t\t\t\tphone: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请输入手机号',\r\n\t\t\t\t\t\ttrigger: 'change'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n            pattern: /^1[3-9]\\d{9}$/,\r\n            message: '请输入正确的手机号',\r\n            trigger: 'change'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tcarNo:{\r\n          required: true,\r\n          message: '请输入车牌号',\r\n          trigger: 'change'\r\n        },\r\n\t\t\t\tisTag:{\r\n          required: true,\r\n          message: '请选择是否有标',\r\n          trigger: 'change'\r\n        },\r\n\t\t\t\taddress:{\r\n          required: true,\r\n          message: '请选择或输入地址',\r\n          trigger: 'change'\r\n        },\r\n        detailAdd:{\r\n          required: true,\r\n          message: '请输入详细地址',\r\n          trigger: 'change'\r\n        }\r\n\t\t\t},\r\n\t\t\t// 车型选项\r\n\t\t\tcarTypeColumns: [\r\n\t\t\t\t['轿车', 'SUV', '面包车', '货车', '客车', '摩托车', '电动车', '其他']\r\n\t\t\t],\r\n\t\t\t// 控制显示状态\r\n\t\t\tshowCarTypePicker: false,\r\n\t\t\tshowNumberKeyboard: false,\r\n\t\t\tshowLicensePlatePicker: false,\r\n\t\t\tsubmitting: false\r\n\t\t};\r\n\t},\r\n\tonReady() {\r\n\t\t\t//如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。\r\n\t\tconsole.log(this.rules)\r\n    this.$refs.uForm.setRules(this.rules)\r\n\t},\r\n\tmethods: {\r\n\t\thandlerPhoneNum(val){\r\n\t\t\tthis.form.phone += val;\r\n\t\t},\r\n\t\thandlerPhoneNumBackspace(){\r\n\t\t\tif(this.form.phone.length) this.form.phone = this.form.phone.substr(0, this.form.phone.length - 1);\r\n\t\t},\r\n\t\thandlerLicensePlate(val){\r\n\t\t\tthis.form.carNo += val;\r\n\t\t},\r\n\t\thandlerLicensePlateBackspace(){\r\n\t\t\tif(this.form.carNo.length) this.form.carNo = this.form.carNo.substr(0, this.form.carNo.length - 1);\r\n\t\t},\r\n\t\t// 车型选择确认\r\n\t\tconfirmCarType(value) {\r\n\t\t\tthis.form.carType = value.value[0]\r\n\t\t\tthis.showCarTypePicker = false\r\n\t\t},\r\n\r\n\t\t// 选择位置\r\n\t\tchooseLocation() {\r\n\t\t\tuni.chooseLocation({\r\n\t\t\t\tsuccess: (res) => {\r\n          console.log(res, '获取到的位置')\r\n\t\t\t\t\t// this.form.address = res.address + ' ' + res.name\r\n          this.form.address = res.address\r\n          this.form.longitude = res.longitude\r\n          this.form.latitude = res.latitude\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t// 如果用户拒绝授权，可以手动输入\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '获取位置失败',\r\n\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 获取当前位置\r\n\t\tgetCurrentLocation() {\r\n\t\t\tuni.getLocation({\r\n\t\t\t\ttype: 'gcj02',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t// 可以根据经纬度反向解析地址\r\n\t\t\t\t\tconsole.log('当前位置:', res)\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.log('获取位置失败:', err)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 表单提交\r\n\t\tasync submitForm() {\r\n\t\t\t// 表单验证\r\n\t\t\ttry {\r\n\t\t\t\tawait this.$refs.uForm.validate()\r\n\t\t\t} catch (errors) {\r\n\t\t\t\tconsole.log('表单验证失败:', errors)\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\r\n\t\t\tthis.submitting = true\r\n\r\n\t\t\ttry {\r\n\t\t\t\t// 调用后端接口\r\n\t\t\t\tconst response = await this.submitRescueRequest()\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '提交成功,稍后会有专人电话联系您！',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\r\n\t\t\t\t// 提交成功后可以跳转到结果页面或重置表单\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.resetForm()\r\n\t\t\t\t}, 1500)\r\n\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('提交失败:', error)\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '提交失败，请重试',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t} finally {\r\n\t\t\t\tthis.submitting = false\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 调用后端接口提交救援请求\r\n\t\tasync submitRescueRequest() {\r\n\t\t\tconst requestData = {\r\n\t\t\t\tcarType: this.form.carType,\r\n\t\t\t\tphone: this.form.phone,\r\n\t\t\t\tcarNo: this.form.carNo,\r\n\t\t\t\tdesc: this.form.desc,\r\n\t\t\t\tisTag: this.form.isTag,\r\n\t\t\t\taddress: this.form.address,\r\n        detailAdd: this.form.detailAdd,\r\n\t\t\t\tgroup: this.form.group,\r\n        longitude: this.form.longitude,\r\n        latitude: this.form.latitude\r\n\t\t\t}\r\n\r\n\t\t\t// 调用封装的post方法\r\n\t\t\treturn await post('/system/wx', requestData)\r\n\t\t},\r\n\r\n\t\t// 重置表单\r\n\t\tresetForm() {\r\n\t\t\tthis.form = {\r\n\t\t\t\tcarType: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tcarNo: '',\r\n\t\t\t\tdesc: '',\r\n\t\t\t\tisTag: '',\r\n\t\t\t\taddress: '',\r\n        detailAdd: '',\r\n\t\t\t\tgroup: '',\r\n        longitude: '',\r\n        latitude: ''\r\n\t\t\t}\r\n\t\t\tthis.$refs.uForm.clearValidate()\r\n\t\t}\r\n\t},\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.container{\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: #01529b;\r\n\r\n  .header{\r\n    width: 100%;\r\n    height: 400rpx;\r\n\r\n    image{\r\n      width: 100%;\r\n      height: 100%;\r\n      z-index: 0;\r\n    }\r\n  }\r\n\r\n  .formBox{\r\n    width: 700rpx;\r\n    margin: 0 auto;\r\n    padding: 20rpx 35rpx;\r\n    box-sizing: border-box;\r\n    border-radius:20rpx;\r\n    background: #fff;\r\n    z-index: 888;\r\n  }\r\n\r\n  ::v-deep .u-radio{\r\n    margin-right: 30rpx;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=a3dd9962&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=a3dd9962&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756609794986\n      var cssReload = require(\"D:/soft/HBuilderX.4.36.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}