{"version": 3, "sources": ["webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-input/u-input.vue?79fa", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-input/u-input.vue?fa28", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-input/u-input.vue?a05b", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-input/u-input.vue?221a", "uni-app:///uni_modules/uview-ui/components/u-input/u-input.vue", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-input/u-input.vue?c0de", "webpack:///D:/code/work/rescue/uni_modules/uview-ui/components/u-input/u-input.vue?905d"], "names": ["name", "mixins", "data", "innerValue", "focused", "firstChange", "changeFromInner", "innerFormatter", "watch", "value", "immediate", "handler", "computed", "isShowClear", "readonly", "inputClass", "border", "disabled", "shape", "classes", "wrapperStyle", "style", "inputStyle", "color", "fontSize", "textAlign", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onInput", "onBlur", "uni", "onFocus", "onConfirm", "onkeyboardheightchange", "valueChange", "onClear", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC2M;AAC3M,gBAAgB,4MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAA4yB,CAAgB,4wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC6Eh0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzCA,eA0CA;EACAA;EACAC;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QAAA;MAAA;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QACA;QAUA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QAAAC;QAAAV;QAAAD;MACA;IACA;IACA;IACAY;MACA;QACAC;QAAAC;QAAAC;MACAF,0BACAG;MACAA;MACAH,wBACAG,0BACA,mBACA,qBACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;MACA;MACA;QACAA;MACA;QACA;QACAA;QACAA;QACAA;QACAA;MACA;MACA;IACA;IACA;IACAC;MACA;QACAC;QACAC;QACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QAAA;QAAAnB;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAoB;MAAA;MACA;MACA;MACA;MACAC;QACA;MACA;MACA;MACAA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACAJ;MACA;IACA;IACA;IACAK;MAAA;MACA;MACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC,uCASA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7RA;AAAA;AAAA;AAAA;AAA+gD,CAAgB,+4CAAG,EAAC,C;;;;;;;;;;;ACAniD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-input/u-input.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-input.vue?vue&type=template&id=113bc24f&scoped=true&\"\nvar renderjs\nimport script from \"./u-input.vue?vue&type=script&lang=js&\"\nexport * from \"./u-input.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-input.vue?vue&type=style&index=0&id=113bc24f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"113bc24f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-input/u-input.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=template&id=113bc24f&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.wrapperStyle])\n  var s1 = _vm.__get_style([_vm.inputStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"u-input\" :class=\"inputClass\" :style=\"[wrapperStyle]\">\n        <view class=\"u-input__content\">\n            <view\n                class=\"u-input__content__prefix-icon\"\n                v-if=\"prefixIcon || $slots.prefix\"\n            >\n                <slot name=\"prefix\">\n                    <u-icon\n                        :name=\"prefixIcon\"\n                        size=\"18\"\n                        :customStyle=\"prefixIconStyle\"\n                    ></u-icon>\n                </slot>\n            </view>\n            <view class=\"u-input__content__field-wrapper\" @tap=\"clickHandler\">\n\t\t\t\t<!-- 根据uni-app的input组件文档，H5和APP中只要声明了password参数(无论true还是false)，type均失效，此时\n\t\t\t\t\t为了防止type=number时，又存在password属性，type无效，此时需要设置password为undefined\n\t\t\t\t -->\n            \t<input\n            \t    class=\"u-input__content__field-wrapper__field\"\n            \t    :style=\"[inputStyle]\"\n            \t    :type=\"type\"\n            \t    :focus=\"focus\"\n            \t    :cursor=\"cursor\"\n            \t    :value=\"innerValue\"\n            \t    :auto-blur=\"autoBlur\"\n            \t    :disabled=\"disabled || readonly\"\n            \t    :maxlength=\"maxlength\"\n            \t    :placeholder=\"placeholder\"\n            \t    :placeholder-style=\"placeholderStyle\"\n            \t    :placeholder-class=\"placeholderClass\"\n            \t    :confirm-type=\"confirmType\"\n            \t    :confirm-hold=\"confirmHold\"\n            \t    :hold-keyboard=\"holdKeyboard\"\n            \t    :cursor-spacing=\"cursorSpacing\"\n            \t    :adjust-position=\"adjustPosition\"\n            \t    :selection-end=\"selectionEnd\"\n            \t    :selection-start=\"selectionStart\"\n            \t    :password=\"password || type === 'password' || false\"\n                    :ignoreCompositionEvent=\"ignoreCompositionEvent\"\n            \t    @input=\"onInput\"\n            \t    @blur=\"onBlur\"\n            \t    @focus=\"onFocus\"\n            \t    @confirm=\"onConfirm\"\n            \t    @keyboardheightchange=\"onkeyboardheightchange\"\n            \t/>\n            </view>\n            <view\n                class=\"u-input__content__clear\"\n                v-if=\"isShowClear\"\n                @tap=\"onClear\"\n            >\n                <u-icon\n                    name=\"close\"\n                    size=\"11\"\n                    color=\"#ffffff\"\n                    customStyle=\"line-height: 12px\"\n                ></u-icon>\n            </view>\n            <view\n                class=\"u-input__content__subfix-icon\"\n                v-if=\"suffixIcon || $slots.suffix\"\n            >\n                <slot name=\"suffix\">\n                    <u-icon\n                        :name=\"suffixIcon\"\n                        size=\"18\"\n                        :customStyle=\"suffixIconStyle\"\n                    ></u-icon>\n                </slot>\n            </view>\n        </view>\n    </view>\n</template>\n\n<script>\nimport props from \"./props.js\";\n/**\n * Input 输入框\n * @description  此组件为一个输入框，默认没有边框和样式，是专门为配合表单组件u-form而设计的，利用它可以快速实现表单验证，输入内容，下拉选择等功能。\n * @tutorial https://uviewui.com/components/input.html\n * @property {String | Number}\tvalue\t\t\t\t\t输入的值\n * @property {String}\t\t\ttype\t\t\t\t\t输入框类型，见上方说明 （ 默认 'text' ）\n * @property {Boolean}\t\t\tfixed\t\t\t\t\t如果 textarea 是在一个 position:fixed 的区域，需要显示指定属性 fixed 为 true，兼容性：微信小程序、百度小程序、字节跳动小程序、QQ小程序 （ 默认 false ）\n * @property {Boolean}\t\t\tdisabled\t\t\t\t是否禁用输入框 （ 默认 false ）\n * @property {String}\t\t\tdisabledColor\t\t\t禁用状态时的背景色（ 默认 '#f5f7fa' ）\n * @property {Boolean}\t\t\tclearable\t\t\t\t是否显示清除控件 （ 默认 false ）\n * @property {Boolean}\t\t\tpassword\t\t\t\t是否密码类型 （ 默认 false ）\n * @property {String | Number}\tmaxlength\t\t\t\t最大输入长度，设置为 -1 的时候不限制最大长度 （ 默认 -1 ）\n * @property {String}\t\t\tplaceholder\t\t\t\t输入框为空时的占位符\n * @property {String}\t\t\tplaceholderClass\t\t指定placeholder的样式类，注意页面或组件的style中写了scoped时，需要在类名前写/deep/ （ 默认 'input-placeholder' ）\n * @property {String | Object}\tplaceholderStyle\t\t指定placeholder的样式，字符串/对象形式，如\"color: red;\"\n * @property {Boolean}\t\t\tshowWordLimit\t\t\t是否显示输入字数统计，只在 type =\"text\"或type =\"textarea\"时有效 （ 默认 false ）\n * @property {String}\t\t\tconfirmType\t\t\t\t设置右下角按钮的文字，兼容性详见uni-app文档 （ 默认 'done' ）\n * @property {Boolean}\t\t\tconfirmHold\t\t\t\t点击键盘右下角按钮时是否保持键盘不收起，H5无效 （ 默认 false ）\n * @property {Boolean}\t\t\tholdKeyboard\t\t\tfocus时，点击页面的时候不收起键盘，微信小程序有效 （ 默认 false ）\n * @property {Boolean}\t\t\tfocus\t\t\t\t\t自动获取焦点，在 H5 平台能否聚焦以及软键盘是否跟随弹出，取决于当前浏览器本身的实现。nvue 页面不支持，需使用组件的 focus()、blur() 方法控制焦点 （ 默认 false ）\n * @property {Boolean}\t\t\tautoBlur\t\t\t\t键盘收起时，是否自动失去焦点，目前仅App3.0.0+有效 （ 默认 false ）\n * @property {Boolean}\t\t\tdisableDefaultPadding\t是否去掉 iOS 下的默认内边距，仅微信小程序，且type=textarea时有效 （ 默认 false ）\n * @property {String ｜ Number}\tcursor\t\t\t\t\t指定focus时光标的位置（ 默认 -1 ）\n * @property {String ｜ Number}\tcursorSpacing\t\t\t输入框聚焦时底部与键盘的距离 （ 默认 30 ）\n * @property {String ｜ Number}\tselectionStart\t\t\t光标起始位置，自动聚集时有效，需与selection-end搭配使用 （ 默认 -1 ）\n * @property {String ｜ Number}\tselectionEnd\t\t\t光标结束位置，自动聚集时有效，需与selection-start搭配使用 （ 默认 -1 ）\n * @property {Boolean}\t\t\tadjustPosition\t\t\t键盘弹起时，是否自动上推页面 （ 默认 true ）\n * @property {String}\t\t\tinputAlign\t\t\t\t输入框内容对齐方式（ 默认 'left' ）\n * @property {String | Number}\tfontSize\t\t\t\t输入框字体的大小 （ 默认 '15px' ）\n * @property {String}\t\t\tcolor\t\t\t\t\t输入框字体颜色\t（ 默认 '#303133' ）\n * @property {Function}\t\t\tformatter\t\t\t    内容式化函数\n * @property {String}\t\t\tprefixIcon\t\t\t\t输入框前置图标\n * @property {String | Object}\tprefixIconStyle\t\t\t前置图标样式，对象或字符串\n * @property {String}\t\t\tsuffixIcon\t\t\t\t输入框后置图标\n * @property {String | Object}\tsuffixIconStyle\t\t\t后置图标样式，对象或字符串\n * @property {String}\t\t\tborder\t\t\t\t\t边框类型，surround-四周边框，bottom-底部边框，none-无边框 （ 默认 'surround' ）\n * @property {Boolean}\t\t\treadonly\t\t\t\t是否只读，与disabled不同之处在于disabled会置灰组件，而readonly则不会 （ 默认 false ）\n * @property {String}\t\t\tshape\t\t\t\t\t输入框形状，circle-圆形，square-方形 （ 默认 'square' ）\n * @property {Object}\t\t\tcustomStyle\t\t\t\t定义需要用到的外部样式\n * @property {Boolean}\t\t\tignoreCompositionEvent\t是否忽略组件内对文本合成系统事件的处理。\n * @example <u-input v-model=\"value\" :password=\"true\" suffix-icon=\"lock-fill\" />\n */\nexport default {\n    name: \"u-input\",\n    mixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n    data() {\n        return {\n            // 输入框的值\n            innerValue: \"\",\n            // 是否处于获得焦点状态\n            focused: false,\n            // value是否第一次变化，在watch中，由于加入immediate属性，会在第一次触发，此时不应该认为value发生了变化\n            firstChange: true,\n            // value绑定值的变化是由内部还是外部引起的\n            changeFromInner: false,\n\t\t\t// 过滤处理方法\n\t\t\tinnerFormatter: value => value\n        };\n    },\n    watch: {\n        value: {\n            immediate: true,\n            handler(newVal, oldVal) {\n                this.innerValue = newVal;\n                /* #ifdef H5 */\n                // 在H5中，外部value变化后，修改input中的值，不会触发@input事件，此时手动调用值变化方法\n                if (\n                    this.firstChange === false &&\n                    this.changeFromInner === false\n                ) {\n                    this.valueChange();\n                }\n                /* #endif */\n                this.firstChange = false;\n                // 重置changeFromInner的值为false，标识下一次引起默认为外部引起的\n                this.changeFromInner = false;\n            },\n        },\n    },\n    computed: {\n        // 是否显示清除控件\n        isShowClear() {\n            const { clearable, readonly, focused, innerValue } = this;\n            return !!clearable && !readonly && !!focused && innerValue !== \"\";\n        },\n        // 组件的类名\n        inputClass() {\n            let classes = [],\n                { border, disabled, shape } = this;\n            border === \"surround\" &&\n                (classes = classes.concat([\"u-border\", \"u-input--radius\"]));\n            classes.push(`u-input--${shape}`);\n            border === \"bottom\" &&\n                (classes = classes.concat([\n                    \"u-border-bottom\",\n                    \"u-input--no-radius\",\n                ]));\n            return classes.join(\" \");\n        },\n        // 组件的样式\n        wrapperStyle() {\n            const style = {};\n            // 禁用状态下，被背景色加上对应的样式\n            if (this.disabled) {\n                style.backgroundColor = this.disabledColor;\n            }\n            // 无边框时，去除内边距\n            if (this.border === \"none\") {\n                style.padding = \"0\";\n            } else {\n                // 由于uni-app的iOS开发者能力有限，导致需要分开写才有效\n                style.paddingTop = \"6px\";\n                style.paddingBottom = \"6px\";\n                style.paddingLeft = \"9px\";\n                style.paddingRight = \"9px\";\n            }\n            return uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle));\n        },\n        // 输入框的样式\n        inputStyle() {\n            const style = {\n                color: this.color,\n                fontSize: uni.$u.addUnit(this.fontSize),\n\t\t\t\ttextAlign: this.inputAlign\n            };\n            return style;\n        },\n    },\n    methods: {\n\t\t// 在微信小程序中，不支持将函数当做props参数，故只能通过ref形式调用\n\t\tsetFormatter(e) {\n\t\t\tthis.innerFormatter = e\n\t\t},\n        // 当键盘输入时，触发input事件\n        onInput(e) {\n            let { value = \"\" } = e.detail || {};\n            // 格式化过滤方法\n            const formatter = this.formatter || this.innerFormatter\n            const formatValue = formatter(value)\n            // 为了避免props的单向数据流特性，需要先将innerValue值设置为当前值，再在$nextTick中重新赋予设置后的值才有效\n            this.innerValue = value\n            this.$nextTick(() => {\n            \tthis.innerValue = formatValue;\n            \tthis.valueChange();\n            })\n        },\n        // 输入框失去焦点时触发\n        onBlur(event) {\n            this.$emit(\"blur\", event.detail.value);\n            // H5端的blur会先于点击清除控件的点击click事件触发，导致focused\n            // 瞬间为false，从而隐藏了清除控件而无法被点击到\n            uni.$u.sleep(50).then(() => {\n                this.focused = false;\n            });\n            // 尝试调用u-form的验证方法\n            uni.$u.formValidate(this, \"blur\");\n        },\n        // 输入框聚焦时触发\n        onFocus(event) {\n            this.focused = true;\n            this.$emit(\"focus\");\n        },\n        // 点击完成按钮时触发\n        onConfirm(event) {\n            this.$emit(\"confirm\", this.innerValue);\n        },\n        // 键盘高度发生变化的时候触发此事件\n        // 兼容性：微信小程序2.7.0+、App 3.1.0+\n\t\tonkeyboardheightchange() {\n            this.$emit(\"keyboardheightchange\");\n        },\n        // 内容发生变化，进行处理\n        valueChange() {\n            const value = this.innerValue;\n            this.$nextTick(() => {\n                this.$emit(\"input\", value);\n                // 标识value值的变化是由内部引起的\n                this.changeFromInner = true;\n                this.$emit(\"change\", value);\n                // 尝试调用u-form的验证方法\n                uni.$u.formValidate(this, \"change\");\n            });\n        },\n        // 点击清除控件\n        onClear() {\n            this.innerValue = \"\";\n            this.$nextTick(() => {\n                this.valueChange();\n                this.$emit(\"clear\");\n            });\n        },\n        /**\n         * 在安卓nvue上，事件无法冒泡\n         * 在某些时间，我们希望监听u-from-item的点击事件，此时会导致点击u-form-item内的u-input后\n         * 无法触发u-form-item的点击事件，这里通过手动调用u-form-item的方法进行触发\n         */\n        clickHandler() {\n            // #ifdef APP-NVUE\n            if (uni.$u.os() === \"android\") {\n                const formItem = uni.$u.$parent.call(this, \"u-form-item\");\n                if (formItem) {\n                    formItem.clickHandler();\n                }\n            }\n            // #endif\n        },\n    },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../libs/css/components.scss\";\n\n.u-input {\n    @include flex(row);\n    align-items: center;\n    justify-content: space-between;\n    flex: 1;\n\n    &--radius,\n    &--square {\n        border-radius: 4px;\n    }\n\n    &--no-radius {\n        border-radius: 0;\n    }\n\n    &--circle {\n        border-radius: 100px;\n    }\n\n    &__content {\n        flex: 1;\n        @include flex(row);\n        align-items: center;\n        justify-content: space-between;\n\n        &__field-wrapper {\n            position: relative;\n            @include flex(row);\n            margin: 0;\n            flex: 1;\n\t\t\t\n\t\t\t&__field {\n\t\t\t\tline-height: 26px;\n\t\t\t\ttext-align: left;\n\t\t\t\tcolor: $u-main-color;\n\t\t\t\theight: 24px;\n\t\t\t\tfont-size: 15px;\n\t\t\t\tflex: 1;\n\t\t\t}\n        }\n\n        &__clear {\n            width: 20px;\n            height: 20px;\n            border-radius: 100px;\n            background-color: #c6c7cb;\n            @include flex(row);\n            align-items: center;\n            justify-content: center;\n            transform: scale(0.82);\n            margin-left: 4px;\n        }\n\n        &__subfix-icon {\n            margin-left: 4px;\n        }\n\n        &__prefix-icon {\n            margin-right: 4px;\n        }\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=style&index=0&id=113bc24f&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=style&index=0&id=113bc24f&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756609795262\n      var cssReload = require(\"D:/soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}