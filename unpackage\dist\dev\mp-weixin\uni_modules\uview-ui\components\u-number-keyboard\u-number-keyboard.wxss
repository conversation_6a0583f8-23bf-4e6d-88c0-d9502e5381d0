@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-64362e91, scroll-view.data-v-64362e91, swiper-item.data-v-64362e91 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-keyboard.data-v-64362e91 {
  display: flex;
  flex-direction: row;
  flex-direction: row;
  justify-content: space-around;
  background-color: #e0e4e6;
  flex-wrap: wrap;
  padding: 8px 10rpx 8px 10rpx;
}
.u-keyboard__button-wrapper.data-v-64362e91 {
  box-shadow: 0 2px 0px #BBBCBE;
  margin: 4px 6rpx;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
.u-keyboard__button-wrapper__button.data-v-64362e91 {
  width: 222rpx;
  height: 90rpx;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
.u-keyboard__button-wrapper__button__text.data-v-64362e91 {
  font-size: 20px;
  font-weight: 500;
  color: #303133;
}
.u-keyboard__button-wrapper__button--gray.data-v-64362e91 {
  background-color: #c8cad2;
}
.u-hover-class.data-v-64362e91 {
  background-color: #BBBCC6;
}

