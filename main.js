import App from './App'
import uView from '@/uni_modules/uview-ui'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
import store from './store'

Vue.prototype.$store = store
Vue.config.productionTip = false


App.mpType = 'app'
const app = new Vue({
  store,
  ...App
})
app.$mount()
Vue.use(uView)
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif