<template>
    <view class="container">
      <view class="detailBox">
        <view class="item">
          <view class="lable">车型：</view>
          <view class="value">{{ info.carType }}</view>
        </view>
        <view class="item">
          <view class="lable">手机号：</view>
          <view class="value">{{ info.phone }}</view>
        </view>
        <view class="item">
          <view class="lable">车牌号：</view>
          <view class="value">{{ info.carNo }}</view>
        </view>
        <view class="item">
          <view class="lable">车型：</view>
          <view class="value">{{ info.carType }}</view>
        </view>
        <view class="item">
          <view class="lable">是否有标：</view>
          <view class="value">{{ info.isTag? '是' : '否' }}</view>
        </view>
        <view class="item">
          <view class="lable">地址：</view>
          <view class="value">{{ info.address }}</view>
        </view>
        <view class="item">
          <view class="lable">详细地址：</view>
          <view class="value">{{ info.detailAdd }}</view>
        </view>
        <view class="item">
          <view class="lable">几群：</view>
          <view class="value">{{ info.group }}</view>
        </view>
        <view class="item">
          <view class="lable">救援事项：</view>
          <view class="value">{{ info.desc }}</view>
        </view>
      </view>

      <view class="btnBox">
        <view class="btn flex_c" @click="handlerNavigation">导航</view>
        <view v-if="info.status == 1" class="btn btn1 flex_c" @click="handlerSuccess">救援完成</view>
        <view v-if="info.status == 0" class="btn btn2 flex_c" @click="receivingOrders">接单</view>
      </view>
    </view>
</template>

<script>
import { get, post } from '@/utils/request.js'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      info: {},
      id: ''
    }
  },
  computed: {
    ...mapState(['userInfo'])
  },
  onLoad(options) {
    console.log(options)
    if(options.id){
      this.id = options.id
      this.getDetail(options.id)
    }
  },
  methods: {
    getDetail(id){
      get('/system/wx/getInfo', { id }).then(res => {
        if(res.code == '200'){
          this.info = res.data || {}
        }
      }).catch(err => {
        console.log(err)
      })
    },
    handlerNavigation(){
      uni.openLocation({
        latitude: +this.info.latitude,
        longitude: +this.info.longitude,
        name: this.info.address,
        address: this.info.address
      })
    },
    handlerSuccess(){
      let that = this
      
      uni.showModal({
        title: '提示',
        editable: true,
        placeholderText: '请输入救援结果',
        success: function (res) {
          if (res.confirm) {
            that.complete(res.content)
          }
        }
      });
    },
    complete(content){
      if(!content){
        uni.showToast({
          title: '请输入救援结果',
          icon: 'none',
        })
        return
      }
      let params = {
        id: +this.id,
        jyId: this.userInfo.jyId,
        result: content
      }
      get('/system/wx/done', params).then(res => {
        if(res.code == '200'){
          uni.showToast({
            title: '操作成功',
            icon: 'success'
          })
          this.getDetail(this.id)
        }
      }).catch(err => {
        console.log(err)
      })
    },
    receivingOrders(){
        let that = this
        uni.showModal({
            title: '提示',
            content: '是否接单',
            success: function (res) {
              if (res.confirm) {
                get('/system/wx/jd',{id: that.info.id}).then(res => {
                    if(res.code == '200'){
                        uni.showToast({
                            title: '接单成功',
                            icon: 'none'
                        })
                        setTimeout(() => {
                            that.getDetail(that.info.id)
                        }, 1500);
                    }
                }).catch(err => {
                    console.log(err)
                })
              }
            }
        });
        
    }
  },
}
</script>

<style scoped lang="scss">
  .container{
    width: 100%;
    padding: 30rpx;
    box-sizing: border-box;
    min-height: 100vh;
    background: #f4f4f4;

    .detailBox{
      width: 100%;
      padding: 20rpx;
      box-sizing: border-box;
      background: #fff;
      border-radius: 20rpx;

      .item{
        height: 60rpx;
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;

        .lable{
          width: 170rpx;
          flex-shrink: 0;
        }
        .value{
          flex: 1;
        }
      }
    }

    .btnBox{
      margin-top: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 40rpx;

      .btn{
        width: 200rpx;
        height: 70rpx;
        color: #fff;
        background: #2d89c5;
        border-radius: 10rpx;
      }
      .btn1{
        background: #b31d1d;
      }
      .btn2{
        background: #f1a026;
      }
    }
  }

  .flex_c{
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>