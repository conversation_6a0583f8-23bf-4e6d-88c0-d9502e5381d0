<template>
  <view class="container">
    <view class="navbarBox" :style="{
        marginTop: statusBarHeight + 'px',
        height: navBarHeight + 'px',
      }">
      <view class="release" @click="showTypePopup">
        <u-icon name="plus" color="#44d4a7"></u-icon>
        发布
      </view>
      <view class="city" @click="cityPickerStatus = true">
        {{ city }}
        <u-icon name="arrow-down-fill" color="#000"></u-icon>
      </view>
    </view>

    <scroll-view 
        scroll-y="true" 
        class="scrollBox" 
        refresher-enabled
        :refresher-triggered="refresherTriggered"
        @refresherpulling="handlerRefresher" 
        @scrolltolower="handlerTolower"
        lower-threshold="100" :style="{ height: `calc(100vh - ${statusBarHeight}px - ${navBarHeight}px)` }"
    >
      <view class="contentBox">
        <view class="item" v-for="(item, index) in postsList" :key="item" @click="toDetail(item)">
          <view class="header">
            <view class="left">
              <image class="avater" :src="item.picUrl" />
              <view class="info">
                <view class="name">{{ item.nickName }}</view>
                <view class="time">{{ item.city }}{{ item.createdAt }}</view>
              </view>
            </view>
            <u-icon class="right" name="more-dot-fill" color="#999" @click.stop="handlerMore(item)"></u-icon>
          </view>

          <view class="content">
            <view class="textBox">{{ item.content }}</view>
            <view class="mideo">
              <image v-for="(el,idx) in item.postMediaList" :key="idx" lazy-load :src="el" mode="widthFix" />
            </view>
          </view>

            <view class="bottomBtn">
                <view class="iconBox">
                    <button class="shareBtn" open-type="share" @click.stop="handlerClick('share', item, index)">
                        <u-icon name="share-square" color="#999" size="23"></u-icon>
                        <text>{{ item.fxCount }}</text>
                    </button>
                </view>
                <view class="iconBox">
                    <u-icon name="file-text" color="#999" size="23"></u-icon>
                    <text>{{ item.comments.length }}</text>
                </view>
                <view class="iconBox" @click.stop="handlerClick('thumbUp', item, index)">
                    <u-icon name="thumb-up" :color="item.isLikes? '#feae61': '#999'" size="23"></u-icon>
                    <text>{{ item.likeCount }}</text>
                </view>
            </view>
        </view>
      </view>
      <view v-if="postsList.length == 0">
        <u-empty></u-empty>
      </view>
    </scroll-view>

    <u-popup :show="releaseStatus" mode="center" round="10" :safeAreaInsetBottom="false" @close="releaseStatus = false">
      <view class="releaseList">
        <view class="item" @click="handlerToRelease(0)">
          <image src="/static/text.png" />
          <text>写心情</text>
        </view>
        <view class="item" @click="handlerToRelease(1)">
          <image src="/static/picture.png" />
          <text>图片</text>
        </view>
        <view class="item" @click="handlerToRelease(2)">
          <image src="/static/video.png" />
          <text>视频</text>
        </view>
      </view>
    </u-popup>

    <u-picker :show="cityPickerStatus" :columns="[cityList]" @confirm="confirm" @close="cityPickerStatus = false" @cancel="cityPickerStatus = false" closeOnClickOverlay></u-picker>
    <u-action-sheet :actions="list" :show="moreStatus" cancelText="取消"  @select="moreSheetClick" @close="moreStatus = false"></u-action-sheet>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { get, post } from '@/utils/request.js'

export default {
    data () {
        return {
            statusBarHeight: 0,
            navBarHeight: 0,
            releaseStatus: false,
            cityPickerStatus: false,

            postsList: [],

            moreStatus: false,
            moreList: [{
                name:'删除'
            }],

            params: {
                pageNum: 1,
                pageSize: 20
            },

            refresherTriggered: false

        }
    },
    computed: {
        ...mapState(['city', 'cityList']),
    },
    onLoad (options) {
        const systemInfo = uni.getSystemInfoSync()
        this.statusBarHeight = systemInfo.statusBarHeight

        const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
        if (menuButtonInfo) {
            this.navBarHeight = (menuButtonInfo.top - this.statusBarHeight) * 2 + menuButtonInfo.height
        }
        if (!this.cityList.length) {
            this.getCityList()
        }
        this.getPostsList()
    },
    methods: {
        getCityList () {
            get('/system/dict/data/type/sys_city').then((res) => {
                if (res.code == '200') {
                    let _cityList = res.data.map((el) => el.dictLabel)
                    this.$store.commit('setCityList', _cityList)
                }
            })
        },
        getPostsList(){
            get('/system/wx/posts/list', this.params).then((res) => {
                if (this.params.pageNum == 1) {
                    this.postsList = res.rows || []
                }else{
                    this.postsList = this.postsList.concat(res.rows || [])
                }
            }).finally(() => {
                this.refresherTriggered = false
            })
        },
        showTypePopup () {
            this.releaseStatus = true
        },
        handlerRefresher () { 
            this.refresherTriggered = true
            this.params.pageNum = 1
            this.getPostsList()
        },
        handlerTolower () {
            this.params.pageNum += 1
            this.getPostsList()
         },

        handlerToRelease (type) {
            uni.navigateTo({
                url: '/pages/circle/release?type=' + type,
            })
        },
        toDetail (item) {
            uni.navigateTo({
                url: '/pages/circle/detail?id=' + item.id,
            })
        },
        confirm(e){
            let cityName = e.value[0]
            this.$store.commit('setCity', cityName)
            this.cityPickerStatus = false
        },
        handlerClick(type, data, index){
            let params = {}
            switch (type) {
                case 'share':
                    params = {
                        postId: data.id,
                        content: data.content,
                        slt: data.slt,
                        mediaType: data.mediaType
                    }
                    this.sharePosts(params, index)
                    break;
                case 'thumbUp':
                     params = {
                        postId: data.id,
                        like: data.isLikes? 0 : 1
                    }
                    this.thumbUpPosts(params, index)
                    break;
                default:
                    break;
            }
        },
        sharePosts(data, index){
            post('/system/wx/posts/fx',data).then((res) => {
                if (res.code == '200') {
                    this.$set(this.postsList[index], 'fxCount', this.postsList[index].fxCount + 1)
                    this.shareFn(data)
                }
            })
        },
        shareFn(data){
            let typeMap = {
                0: 1,
                1: 0,
                2: 4
            }
            uni.share({
                provider: "weixin",
                scene: "WXSceneSession",
                type: typeMap[data.mediaType],
                href: "/pages/circle/detail?id=" + data.postId,
                title: '好有分享给您一条消息',
                summary: data.content,
                imageUrl: data.slt || null,
                success: function (res) {
                    console.log("success:" + JSON.stringify(res));
                },
                fail: function (err) {
                    console.log("fail:" + JSON.stringify(err));
                }
            });
        },
        thumbUpPosts(data, index){
            post('/system/wx/posts/likes',data).then((res) => {
                if (res.code == '200') {
                    this.$set(this.postsList[index], 'isLikes', this.postsList[index].isLikes == 1? 0: 1)
                    this.$set(this.postsList[index], 'likeCount', this.postsList[index].isLikes == 1?this.postsList[index].likeCount + 1: this.postsList[index].likeCount - 1)
                }
            })
        },
        handlerMore(item){

        }

    },
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  padding: 0rpx 25rpx;
  overflow: hidden;
  box-sizing: border-box;
  background: linear-gradient(to bottom, #fcc7ba 0%, #ffffff 30%);

  .navbarBox {
    display: flex;
    align-items: center;
    gap: 30rpx;

    .release,
    .city {
      display: flex;
      gap: 10rpx;
      padding: 10rpx 30rpx;
      color: #020202;
      background: rgba($color: #ffffff, $alpha: 0.6);
      border: 1rpx solid #ff9012;
      border-radius: 30rpx;
    }
  }

  .scrollBox {
    width: 100%;

    .contentBox .item {
      width: 100%;
      padding: 50rpx 0rpx 30rpx;
      border-bottom: 1rpx solid #ececec;
      box-sizing: border-box;

      .header {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 30rpx;
        gap: 30rpx;

        .left {
            flex: 1;
          display: flex;
          align-items: center;
          gap: 10rpx;
          overflow: hidden;
        }
        .left .avater {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          flex-shrink: 0;
        }
        .left .info{
            flex: 1;
            overflow: hidden;
        }
        .left .info .name {
            flex: 1;
            font-size: 35rpx;
            color: #020202;
            line-height: 45rpx;
            font-weight: 500;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
        .left .info .time {
          font-size: 28rpx;
          color: #999;
          line-height: 41rpx;
        }

        .right{
            flex-shrink: 0;
        }
      }

      .content {
        width: 100%;

        .textBox {
          font-size: 30rpx;
          color: #020202;
          line-height: 45rpx;
          white-space: wrap;
          margin-bottom: 20rpx;
        }

        .mideo {
          width: 100%;
          display: flex;
          gap: 20rpx 20rpx;
          flex-wrap: wrap;

          image {
            flex: 1;
            min-width: 30%;
            border-radius: 16rpx;
          }
        }
      }
      .bottomBtn {
        display: flex;
        justify-content: flex-end;
        gap: 50rpx;
        margin-top: 30rpx;

        .iconBox {
          display: flex;
          align-items: center;
          font-size: 32rpx;
          color: #999;
          gap: 8rpx;

          .shareBtn{
            display: flex;
            font-size: 32rpx;
            color: #999;
            background: transparent;

            &::after{
              display: none;
            }
          }
        }
      }
    }
  }

  .releaseList {
    padding: 50rpx;
    display: flex;
    gap: 30rpx;
    border-radius: 20rpx;
    overflow: hidden;
    box-sizing: border-box;

    .item {
      width: 90rpx;
      padding: 20rpx 30rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 10rpx;
      font-size: 29rpx;
      font-weight: bold;
      color: #020202;
      background: rgba($color: #c5c4c4, $alpha: 0.2);
      border-radius: 10rpx;

      image {
        width: 50rpx;
        height: 50rpx;
      }
    }
  }
}
</style>
