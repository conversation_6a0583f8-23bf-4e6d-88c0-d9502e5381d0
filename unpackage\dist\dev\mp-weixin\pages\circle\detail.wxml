<view class="container data-v-8775d272"><view class="headerBox data-v-8775d272"><view class="left data-v-8775d272"><image class="avater data-v-8775d272" src="{{detailInfo.picUrl}}"></image><view class="info data-v-8775d272"><view class="name data-v-8775d272">{{detailInfo.nickName}}</view><view class="time data-v-8775d272">{{detailInfo.createdAt}}</view></view></view></view><view class="detailBox data-v-8775d272"><view class="textBox data-v-8775d272">{{''+detailInfo.content+''}}</view><view class="mideo data-v-8775d272"><block wx:for="{{detailInfo.postMediaList}}" wx:for-item="el" wx:for-index="idx" wx:key="idx"><image lazy-load="{{true}}" src="{{el}}" mode="widthFix" class="data-v-8775d272"></image></block></view></view><view class="cityInfo data-v-8775d272"><view class="city data-v-8775d272">{{detailInfo.city}}</view></view><view class="commentBox data-v-8775d272"><view class="title data-v-8775d272">{{"评论("+$root.g0+")"}}</view><view class="commentList data-v-8775d272"><block wx:for="{{detailInfo.comments}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="commentItem data-v-8775d272"><view class="com_header data-v-8775d272"><view class="left data-v-8775d272"><image src="{{item.picUrl}}" class="data-v-8775d272"></image><text class="data-v-8775d272">{{item.nickName}}</text></view><u-icon class="right data-v-8775d272" vue-id="{{'674379c2-1-'+index}}" name="more-dot-fill" color="#999" data-event-opts="{{[['^click',[['handlerMore',['$0'],[[['detailInfo.comments','',index]]]]]]]}}" bind:click="__e" bind:__l="__l"></u-icon></view><view class="com_text data-v-8775d272">{{item.content}}</view><view class="com_info data-v-8775d272"><view class="timeCity data-v-8775d272">五个月前 深圳</view></view></view></block></view></view><view class="inputModule data-v-8775d272"><view class="inputBox data-v-8775d272"><input class="uni-input data-v-8775d272 vue-ref" id="inputRef" placeholder="说点什么吧..." type="text" data-ref="inputRef" data-event-opts="{{[['input',[['__set_model',['','commentContent','$event',[]]]]]]}}" value="{{commentContent}}" bindinput="__e"/></view><view class="operateBox data-v-8775d272"><view data-event-opts="{{[['tap',[['addComment',['$event']]]]]}}" class="sendBtn data-v-8775d272" bindtap="__e">提交</view><view data-event-opts="{{[['tap',[['thumbUpPosts',['$event']]]]]}}" class="iconBox data-v-8775d272" bindtap="__e"><u-icon vue-id="674379c2-2" name="thumb-up" color="{{detailInfo.isLikes?'#feae61':'#999'}}" size="23" class="data-v-8775d272" bind:__l="__l"></u-icon><text class="data-v-8775d272">{{detailInfo.likeCount}}</text></view></view></view><u-action-sheet vue-id="674379c2-3" actions="{{list}}" show="{{commentStatus}}" cancelText="取消" data-event-opts="{{[['^select',[['selectClick']]],['^close',[['e0']]]]}}" bind:select="__e" bind:close="__e" class="data-v-8775d272" bind:__l="__l"></u-action-sheet></view>