@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-8775d272 {
  width: 100%;
  min-height: 100vh;
  padding: 30rpx 30rpx 130rpx;
  box-sizing: border-box;
  background: #f5f5f5;
}
.container .headerBox.data-v-8775d272 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}
.container .headerBox .left.data-v-8775d272 {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10rpx;
  overflow: hidden;
}
.container .headerBox .left .avater.data-v-8775d272 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  flex-shrink: 0;
}
.container .headerBox .left .info.data-v-8775d272 {
  flex: 1;
  overflow: hidden;
}
.container .headerBox .left .info .name.data-v-8775d272 {
  flex: 1;
  font-size: 35rpx;
  color: #020202;
  line-height: 45rpx;
  font-weight: 500;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.container .headerBox .left .info .time.data-v-8775d272 {
  font-size: 28rpx;
  color: #999;
  line-height: 41rpx;
}
.container .headerBox .right.data-v-8775d272 {
  flex-shrink: 0;
}
.container .detailBox.data-v-8775d272 {
  width: 100%;
  margin-top: 30rpx;
}
.container .detailBox .textBox.data-v-8775d272 {
  font-size: 30rpx;
  color: #020202;
  line-height: 45rpx;
  white-space: wrap;
  margin-bottom: 20rpx;
}
.container .detailBox .mideo.data-v-8775d272 {
  width: 100%;
  display: flex;
  gap: 20rpx 20rpx;
  flex-wrap: wrap;
}
.container .detailBox .mideo image.data-v-8775d272 {
  flex: 1;
  min-width: 30%;
  border-radius: 16rpx;
}
.container .cityInfo.data-v-8775d272 {
  width: 100%;
  display: flex;
  justify-content: space-between;
  color: #999;
  margin: 30rpx 0;
  border-bottom: 1rpx solid #eeeeee;
  padding-bottom: 20rpx;
}
.container .cityInfo .browseNum.data-v-8775d272 {
  display: flex;
  gap: 10rpx;
}
.container .commentBox.data-v-8775d272 {
  width: 100%;
}
.container .commentBox .title.data-v-8775d272 {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.container .commentBox .commentItem.data-v-8775d272 {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f3f3f3;
}
.container .commentBox .commentItem .com_header.data-v-8775d272 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.container .commentBox .commentItem .com_header .left.data-v-8775d272 {
  display: flex;
  align-items: center;
  gap: 15rpx;
  font-size: 26rpx;
  font-weight: bold;
}
.container .commentBox .commentItem .com_header .left image.data-v-8775d272 {
  width: 55rpx;
  height: 55rpx;
  border-radius: 50%;
}
.container .commentBox .commentItem .com_text.data-v-8775d272 {
  font-size: 28rpx;
  line-height: 35rpx;
}
.container .commentBox .com_info.data-v-8775d272 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
}
.container .commentBox .com_info .like.data-v-8775d272 {
  display: flex;
  align-items: center;
}
.container .commentBox .repliesBox.data-v-8775d272 {
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
  background: #ebe9e9;
  border-radius: 16rpx;
  margin-top: 10rpx;
  font-size: 24rpx;
}
.container .commentBox .repliesBox .repliesItem.data-v-8775d272 {
  padding: 5rpx 10rpx;
  border-bottom: 1rpx solid #ebe8e8;
}
.container .commentBox .repliesBox .repliesItem .label.data-v-8775d272 {
  font-weight: bold;
}
.container .inputModule.data-v-8775d272 {
  width: 100%;
  height: 130rpx;
  position: fixed;
  bottom: 0rpx;
  left: 0rpx;
  padding: 0 20rpx;
  display: flex;
  gap: 20rpx;
  align-items: center;
  box-sizing: border-box;
  background: #fff;
}
.container .inputModule .inputBox.data-v-8775d272 {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.container .inputModule .inputBox .uni-input.data-v-8775d272 {
  flex: 1;
  height: 70rpx;
  border-radius: 50rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  background: rgba(0, 0, 0, 0.1);
}
.container .inputModule .sendBtn.data-v-8775d272 {
  flex-shrink: 0;
  padding: 10rpx 15rpx;
  font-size: 30rpx;
  color: #fff;
  background: #f7991e;
  border-radius: 16rpx;
}
.container .inputModule .operateBox.data-v-8775d272 {
  display: flex;
  justify-content: flex-end;
  gap: 30rpx;
}
.container .inputModule .operateBox .iconBox.data-v-8775d272 {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #999;
  gap: 8rpx;
}

