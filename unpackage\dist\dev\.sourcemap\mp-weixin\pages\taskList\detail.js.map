{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/code/work/rescue/pages/taskList/detail.vue?a1c6", "webpack:///D:/code/work/rescue/pages/taskList/detail.vue?f60b", "webpack:///D:/code/work/rescue/pages/taskList/detail.vue?b22f", "webpack:///D:/code/work/rescue/pages/taskList/detail.vue?2662", "uni-app:///pages/taskList/detail.vue", "webpack:///D:/code/work/rescue/pages/taskList/detail.vue?ee01", "webpack:///D:/code/work/rescue/pages/taskList/detail.vue?6053"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "info", "id", "computed", "onLoad", "console", "methods", "getDetail", "handlerNavigation", "uni", "latitude", "longitude", "name", "address", "handlerSuccess", "title", "editable", "placeholderText", "success", "that", "complete", "icon", "jyId", "result", "receivingOrders", "content", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACqM;AACrM,gBAAgB,4MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6wB,CAAgB,2wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACkDjyB;AACA;AAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC,4BACA,kCACA;EACAC;IACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QAAAL;MAAA;QACA;UACA;QACA;MACA;QACAG;MACA;IACA;IACAG;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MAEAL;QACAM;QACAC;QACAC;QACAC;UACA;YACAC;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACAX;UACAM;UACAM;QACA;QACA;MACA;MACA;QACAnB;QACAoB;QACAC;MACA;MACA;QACA;UACAd;YACAM;YACAM;UACA;UACA;QACA;MACA;QACAhB;MACA;IACA;IACAmB;MACA;MACAf;QACAM;QACAU;QACAP;UACA;YACA;cAAAhB;YAAA;cACA;gBACAO;kBACAM;kBACAM;gBACA;gBACAK;kBACAP;gBACA;cACA;YACA;cACAd;YACA;UACA;QACA;MACA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxJA;AAAA;AAAA;AAAA;AAAw9C,CAAgB,84CAAG,EAAC,C;;;;;;;;;;;ACA5+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/taskList/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/taskList/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=0965eb98&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=0965eb98&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0965eb98\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/taskList/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=0965eb98&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view class=\"container\">\r\n      <view class=\"detailBox\">\r\n        <view class=\"item\">\r\n          <view class=\"lable\">车型：</view>\r\n          <view class=\"value\">{{ info.carType }}</view>\r\n        </view>\r\n        <view class=\"item\">\r\n          <view class=\"lable\">手机号：</view>\r\n          <view class=\"value\">{{ info.phone }}</view>\r\n        </view>\r\n        <view class=\"item\">\r\n          <view class=\"lable\">车牌号：</view>\r\n          <view class=\"value\">{{ info.carNo }}</view>\r\n        </view>\r\n        <view class=\"item\">\r\n          <view class=\"lable\">车型：</view>\r\n          <view class=\"value\">{{ info.carType }}</view>\r\n        </view>\r\n        <view class=\"item\">\r\n          <view class=\"lable\">是否有标：</view>\r\n          <view class=\"value\">{{ info.isTag? '是' : '否' }}</view>\r\n        </view>\r\n        <view class=\"item\">\r\n          <view class=\"lable\">地址：</view>\r\n          <view class=\"value\">{{ info.address }}</view>\r\n        </view>\r\n        <view class=\"item\">\r\n          <view class=\"lable\">详细地址：</view>\r\n          <view class=\"value\">{{ info.detailAdd }}</view>\r\n        </view>\r\n        <view class=\"item\">\r\n          <view class=\"lable\">几群：</view>\r\n          <view class=\"value\">{{ info.group }}</view>\r\n        </view>\r\n        <view class=\"item\">\r\n          <view class=\"lable\">救援事项：</view>\r\n          <view class=\"value\">{{ info.desc }}</view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"btnBox\">\r\n        <view class=\"btn flex_c\" @click=\"handlerNavigation\">导航</view>\r\n        <view v-if=\"info.status == 1\" class=\"btn btn1 flex_c\" @click=\"handlerSuccess\">救援完成</view>\r\n        <view v-if=\"info.status == 0\" class=\"btn btn2 flex_c\" @click=\"receivingOrders\">接单</view>\r\n      </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nimport { get, post } from '@/utils/request.js'\r\nimport { mapState } from 'vuex'\r\nexport default {\r\n  data() {\r\n    return {\r\n      info: {},\r\n      id: ''\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState(['userInfo'])\r\n  },\r\n  onLoad(options) {\r\n    console.log(options)\r\n    if(options.id){\r\n      this.id = options.id\r\n      this.getDetail(options.id)\r\n    }\r\n  },\r\n  methods: {\r\n    getDetail(id){\r\n      get('/system/wx/getInfo', { id }).then(res => {\r\n        if(res.code == '200'){\r\n          this.info = res.data || {}\r\n        }\r\n      }).catch(err => {\r\n        console.log(err)\r\n      })\r\n    },\r\n    handlerNavigation(){\r\n      uni.openLocation({\r\n        latitude: +this.info.latitude,\r\n        longitude: +this.info.longitude,\r\n        name: this.info.address,\r\n        address: this.info.address\r\n      })\r\n    },\r\n    handlerSuccess(){\r\n      let that = this\r\n      \r\n      uni.showModal({\r\n        title: '提示',\r\n        editable: true,\r\n        placeholderText: '请输入救援结果',\r\n        success: function (res) {\r\n          if (res.confirm) {\r\n            that.complete(res.content)\r\n          }\r\n        }\r\n      });\r\n    },\r\n    complete(content){\r\n      if(!content){\r\n        uni.showToast({\r\n          title: '请输入救援结果',\r\n          icon: 'none',\r\n        })\r\n        return\r\n      }\r\n      let params = {\r\n        id: +this.id,\r\n        jyId: this.userInfo.jyId,\r\n        result: content\r\n      }\r\n      get('/system/wx/done', params).then(res => {\r\n        if(res.code == '200'){\r\n          uni.showToast({\r\n            title: '操作成功',\r\n            icon: 'success'\r\n          })\r\n          this.getDetail(this.id)\r\n        }\r\n      }).catch(err => {\r\n        console.log(err)\r\n      })\r\n    },\r\n    receivingOrders(){\r\n        let that = this\r\n        uni.showModal({\r\n            title: '提示',\r\n            content: '是否接单',\r\n            success: function (res) {\r\n              if (res.confirm) {\r\n                get('/system/wx/jd',{id: that.info.id}).then(res => {\r\n                    if(res.code == '200'){\r\n                        uni.showToast({\r\n                            title: '接单成功',\r\n                            icon: 'none'\r\n                        })\r\n                        setTimeout(() => {\r\n                            that.getDetail(that.info.id)\r\n                        }, 1500);\r\n                    }\r\n                }).catch(err => {\r\n                    console.log(err)\r\n                })\r\n              }\r\n            }\r\n        });\r\n        \r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n  .container{\r\n    width: 100%;\r\n    padding: 30rpx;\r\n    box-sizing: border-box;\r\n    min-height: 100vh;\r\n    background: #f4f4f4;\r\n\r\n    .detailBox{\r\n      width: 100%;\r\n      padding: 20rpx;\r\n      box-sizing: border-box;\r\n      background: #fff;\r\n      border-radius: 20rpx;\r\n\r\n      .item{\r\n        height: 60rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 20rpx;\r\n\r\n        .lable{\r\n          width: 170rpx;\r\n          flex-shrink: 0;\r\n        }\r\n        .value{\r\n          flex: 1;\r\n        }\r\n      }\r\n    }\r\n\r\n    .btnBox{\r\n      margin-top: 40rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      gap: 40rpx;\r\n\r\n      .btn{\r\n        width: 200rpx;\r\n        height: 70rpx;\r\n        color: #fff;\r\n        background: #2d89c5;\r\n        border-radius: 10rpx;\r\n      }\r\n      .btn1{\r\n        background: #b31d1d;\r\n      }\r\n      .btn2{\r\n        background: #f1a026;\r\n      }\r\n    }\r\n  }\r\n\r\n  .flex_c{\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n</style>", "import mod from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=0965eb98&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=0965eb98&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756734967541\n      var cssReload = require(\"D:/soft/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}