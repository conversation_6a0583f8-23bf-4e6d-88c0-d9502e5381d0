@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-2c7bad3f {
  width: 1750rpx;
  min-height: 100vh;
  background: #f8f8f8;
}
.container .listBox.data-v-2c7bad3f {
  width: 750rpx;
  padding: 30rpx;
  box-sizing: border-box;
  overflow: hidden;
}
.container .listBox .listItem.data-v-2c7bad3f {
  width: 690rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  background: #fff;
  box-sizing: border-box;
  position: relative;
}
.container .listBox .listItem .item.data-v-2c7bad3f {
  height: 48rpx;
  display: flex;
  align-items: center;
}
.container .listBox .listItem .item .lable.data-v-2c7bad3f {
  width: 170rpx;
  flex-shrink: 0;
}
.container .listBox .listItem .item .value.data-v-2c7bad3f {
  flex: 1;
}
.container .listBox .listItem .status.data-v-2c7bad3f {
  position: absolute;
  right: 0rpx;
  top: 0rpx;
  font-size: 22rpx;
  padding: 5rpx 8rpx;
  background: #dbdbdb;
  border-radius: 0 16rpx 0rpx 16rpx;
}
.container .listBox .listItem .statusProcessing.data-v-2c7bad3f {
  color: #fff;
  background: #6bc900;
}
.container .listBox .listItem .statusSuccess.data-v-2c7bad3f {
  color: #fff;
  background: #0079c9;
}

